(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[340],{5051:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},3327:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},1769:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},2222:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},44:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},1047:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},401:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},875:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},2135:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},1671:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1723:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},8997:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},4938:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},1817:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},9345:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},3774:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4656:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("Quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])},4743:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},8906:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},6595:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},525:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},2369:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},5805:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},9374:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9205).Z)("Video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},3145:function(e,t,r){"use strict";r.d(t,{default:function(){return o.a}});var n=r(8461),o=r.n(n)},5878:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return w}});let n=r(7043),o=r(3099),a=r(7437),i=o._(r(2265)),l=n._(r(4887)),u=n._(r(758)),c=r(5346),s=r(128),d=r(2589);r(1765);let f=r(5523),p=n._(r(5084)),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0};function v(e,t,r,n,o,a,i){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&o(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,o=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>o,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{o=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function m(e){return i.use?{fetchPriority:e}:{fetchpriority:e}}"undefined"==typeof window&&(globalThis.__NEXT_IMAGE_IMPORTED=!0);let y=(0,i.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:o,height:l,width:u,decoding:c,className:s,style:d,fetchPriority:f,placeholder:p,loading:h,unoptimized:y,fill:g,onLoadRef:w,onLoadingCompleteRef:b,setBlurComplete:x,setShowAltText:S,sizesInput:E,onLoad:O,onError:C,...j}=e;return(0,a.jsx)("img",{...j,...m(f),loading:h,width:u,height:l,decoding:c,"data-nimg":g?"fill":"1",className:s,style:d,sizes:o,srcSet:n,src:r,ref:(0,i.useCallback)(e=>{t&&("function"==typeof t?t(e):"object"==typeof t&&(t.current=e)),e&&(C&&(e.src=e.src),e.complete&&v(e,p,w,b,x,y,E))},[r,p,w,b,x,C,y,E,t]),onLoad:e=>{v(e.currentTarget,p,w,b,x,y,E)},onError:e=>{S(!0),"empty"!==p&&x(!0),C&&C(e)}})});function g(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...m(r.fetchPriority)};return t&&l.default.preload?(l.default.preload(r.src,n),null):(0,a.jsx)(u.default,{children:(0,a.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let w=(0,i.forwardRef)((e,t)=>{let r=(0,i.useContext)(f.RouterContext),n=(0,i.useContext)(d.ImageConfigContext),o=(0,i.useMemo)(()=>{var e;let t=h||n||s.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),o=t.deviceSizes.sort((e,t)=>e-t),a=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:o,qualities:a}},[n]),{onLoad:l,onLoadingComplete:u}=e,v=(0,i.useRef)(l);(0,i.useEffect)(()=>{v.current=l},[l]);let m=(0,i.useRef)(u);(0,i.useEffect)(()=>{m.current=u},[u]);let[w,b]=(0,i.useState)(!1),[x,S]=(0,i.useState)(!1),{props:E,meta:O}=(0,c.getImgProps)(e,{defaultLoader:p.default,imgConf:o,blurComplete:w,showAltText:x});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y,{...E,unoptimized:O.unoptimized,placeholder:O.placeholder,fill:O.fill,onLoadRef:v,onLoadingCompleteRef:m,setBlurComplete:b,setShowAltText:S,sizesInput:e.sizes,ref:t}),O.priority?(0,a.jsx)(g,{isAppRouter:!r,imgAttributes:E}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1436:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return n}});let n=r(7043)._(r(2265)).default.createContext({})},3964:function(e,t){"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},5346:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(1765);let n=r(6496),o=r(128);function a(e){return void 0!==e.default}function i(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let u,c,s,{src:d,sizes:f,unoptimized:p=!1,priority:h=!1,loading:v,className:m,quality:y,width:g,height:w,fill:b=!1,style:x,overrideSrc:S,onLoad:E,onLoadingComplete:O,placeholder:C="empty",blurDataURL:j,fetchPriority:k,decoding:D="async",layout:T,objectFit:P,objectPosition:R,lazyBoundary:M,lazyRoot:_,...A}=e,{imgConf:N,showAltText:L,blurComplete:I,defaultLoader:W}=t,F=N||o.imageConfigDefault;if("allSizes"in F)u=F;else{let e=[...F.deviceSizes,...F.imageSizes].sort((e,t)=>e-t),t=F.deviceSizes.sort((e,t)=>e-t),n=null==(r=F.qualities)?void 0:r.sort((e,t)=>e-t);u={...F,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===W)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let V=A.loader||W;delete A.loader,delete A.srcSet;let z="__next_img_default"in V;if(z){if("custom"===u.loader)throw Error('Image with src "'+d+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=V;V=t=>{let{config:r,...n}=t;return e(n)}}if(T){"fill"===T&&(b=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[T];e&&(x={...x,...e});let t={responsive:"100vw",fill:"100vw"}[T];t&&!f&&(f=t)}let H="",B=i(g),Y=i(w);if("object"==typeof(l=d)&&(a(l)||void 0!==l.src)){let e=a(d)?d.default:d;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(c=e.blurWidth,s=e.blurHeight,j=j||e.blurDataURL,H=e.src,!b){if(B||Y){if(B&&!Y){let t=B/e.width;Y=Math.round(e.height*t)}else if(!B&&Y){let t=Y/e.height;B=Math.round(e.width*t)}}else B=e.width,Y=e.height}}let Z=!h&&("lazy"===v||void 0===v);(!(d="string"==typeof d?d:H)||d.startsWith("data:")||d.startsWith("blob:"))&&(p=!0,Z=!1),u.unoptimized&&(p=!0),z&&d.endsWith(".svg")&&!u.dangerouslyAllowSVG&&(p=!0),h&&(k="high");let U=i(y),$=Object.assign(b?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:P,objectPosition:R}:{},L?{}:{color:"transparent"},x),q=I||"empty"===C?null:"blur"===C?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:B,heightInt:Y,blurWidth:c,blurHeight:s,blurDataURL:j||"",objectFit:$.objectFit})+'")':'url("'+C+'")',X=q?{backgroundSize:$.objectFit||"cover",backgroundPosition:$.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:q}:{},G=function(e){let{config:t,src:r,unoptimized:n,width:o,quality:a,sizes:i,loader:l}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:u,kind:c}=function(e,t,r){let{deviceSizes:n,allSizes:o}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);n)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:o.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:o,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>o.find(t=>t>=e)||o[o.length-1]))],kind:"x"}}(t,o,i),s=u.length-1;return{sizes:i||"w"!==c?i:"100vw",srcSet:u.map((e,n)=>l({config:t,src:r,quality:a,width:e})+" "+("w"===c?e:n+1)+c).join(", "),src:l({config:t,src:r,quality:a,width:u[s]})}}({config:u,src:d,unoptimized:p,width:B,quality:U,sizes:f,loader:V});return{props:{...A,loading:Z?"lazy":v,fetchPriority:k,width:B,height:Y,decoding:D,className:m,style:{...$,...X},sizes:G.sizes,srcSet:G.srcSet,src:S||G.src},meta:{unoptimized:p,priority:h,placeholder:C,fill:b}}}},758:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return v},defaultHead:function(){return d}});let n=r(7043),o=r(3099),a=r(7437),i=o._(r(2265)),l=n._(r(7421)),u=r(1436),c=r(8701),s=r(3964);function d(e){void 0===e&&(e=!1);let t=[(0,a.jsx)("meta",{charSet:"utf-8"})];return e||t.push((0,a.jsx)("meta",{name:"viewport",content:"width=device-width"})),t}function f(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===i.default.Fragment?e.concat(i.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(1765);let p=["name","httpEquiv","charSet","itemProp"];function h(e,t){let{inAmpMode:r}=t;return e.reduce(f,[]).reverse().concat(d(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return o=>{let a=!0,i=!1;if(o.key&&"number"!=typeof o.key&&o.key.indexOf("$")>0){i=!0;let t=o.key.slice(o.key.indexOf("$")+1);e.has(t)?a=!1:e.add(t)}switch(o.type){case"title":case"base":t.has(o.type)?a=!1:t.add(o.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(o.props.hasOwnProperty(t)){if("charSet"===t)r.has(t)?a=!1:r.add(t);else{let e=o.props[t],r=n[t]||new Set;("name"!==t||!i)&&r.has(e)?a=!1:(r.add(e),n[t]=r)}}}}return a}}()).reverse().map((e,t)=>{let n=e.key||t;if(!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,i.default.cloneElement(e,t)}return i.default.cloneElement(e,{key:n})})}let v=function(e){let{children:t}=e,r=(0,i.useContext)(u.AmpStateContext),n=(0,i.useContext)(c.HeadManagerContext);return(0,a.jsx)(l.default,{reduceComponentsToState:h,headManager:n,inAmpMode:(0,s.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6496:function(e,t){"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:o,blurDataURL:a,objectFit:i}=e,l=n?40*n:t,u=o?40*o:r,c=l&&u?"viewBox='0 0 "+l+" "+u+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+c+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(c?"none":"contain"===i?"xMidYMid":"cover"===i?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+a+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},2589:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return a}});let n=r(7043)._(r(2265)),o=r(128),a=n.default.createContext(o.imageConfigDefault)},128:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},8461:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return u},getImageProps:function(){return l}});let n=r(7043),o=r(5346),a=r(5878),i=n._(r(5084));function l(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let u=a.Image},5084:function(e,t){"use strict";function r(e){var t;let{config:r,src:n,width:o,quality:a}=e,i=a||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+o+"&q="+i}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},5523:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(7043)._(r(2265)).default.createContext(null)},7421:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let n=r(2265),o="undefined"==typeof window,a=o?()=>{}:n.useLayoutEffect,i=o?()=>{}:n.useEffect;function l(e){let{headManager:t,reduceComponentsToState:r}=e;function l(){if(t&&t.mountedInstances){let o=n.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(o,e))}}if(o){var u;null==t||null==(u=t.mountedInstances)||u.add(e.children),l()}return a(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),a(()=>(t&&(t._pendingUpdate=l),()=>{t&&(t._pendingUpdate=l)})),i(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},6179:function(){},2484:function(e,t,r){"use strict";function n(e,[t,r]){return Math.min(r,Math.max(t,e))}r.d(t,{u:function(){return n}})},6741:function(e,t,r){"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{M:function(){return n}})},3966:function(e,t,r){"use strict";r.d(t,{b:function(){return a}});var n=r(2265),o=r(7437);function a(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return a.scopeName=e,[function(t,a){let i=n.createContext(a),l=r.length;function u(t){let{scope:r,children:a,...u}=t,c=r?.[e][l]||i,s=n.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:a})}return r=[...r,a],u.displayName=t+"Provider",[u,function(r,o){let u=o?.[e][l]||i,c=n.useContext(u);if(c)return c;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(a,...t)]}},9114:function(e,t,r){"use strict";r.d(t,{gm:function(){return a}});var n=r(2265);r(7437);var o=n.createContext(void 0);function a(e){let t=n.useContext(o);return e||t||"ltr"}},6394:function(e,t,r){"use strict";r.d(t,{f:function(){return l}});var n=r(2265),o=r(6840),a=r(7437),i=n.forwardRef((e,t)=>(0,a.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i},6840:function(e,t,r){"use strict";r.d(t,{WV:function(){return l},jH:function(){return u}});var n=r(2265),o=r(4887),a=r(7053),i=r(7437),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...o}=e,l=n?a.g7:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...o,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},1750:function(e,t,r){"use strict";r.d(t,{Ns:function(){return K},fC:function(){return X},gb:function(){return C},q4:function(){return L},l_:function(){return G}});var n=r(2265),o=r(6840),a=r(4887),i=r(8575),l=r(1188),u=e=>{let t,r;let{present:o,children:u}=e,s=function(e){var t,r;let[o,i]=n.useState(),u=n.useRef({}),s=n.useRef(e),d=n.useRef("none"),[f,p]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=c(u.current);d.current="mounted"===f?e:"none"},[f]),(0,l.b)(()=>{let t=u.current,r=s.current;if(r!==e){let n=d.current,o=c(t);e?p("MOUNT"):"none"===o||t?.display==="none"?p("UNMOUNT"):r&&n!==o?p("ANIMATION_OUT"):p("UNMOUNT"),s.current=e}},[e,p]),(0,l.b)(()=>{if(o){let e=e=>{let t=c(u.current).includes(e.animationName);e.target===o&&t&&a.flushSync(()=>p("ANIMATION_END"))},t=e=>{e.target===o&&(d.current=c(u.current))};return o.addEventListener("animationstart",t),o.addEventListener("animationcancel",e),o.addEventListener("animationend",e),()=>{o.removeEventListener("animationstart",t),o.removeEventListener("animationcancel",e),o.removeEventListener("animationend",e)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:n.useCallback(e=>{e&&(u.current=getComputedStyle(e)),i(e)},[])}}(o),d="function"==typeof u?u({present:s.isPresent}):n.Children.only(u),f=(0,i.e)(s.ref,(t=Object.getOwnPropertyDescriptor(d.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?d.ref:(t=Object.getOwnPropertyDescriptor(d,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?d.props.ref:d.props.ref||d.ref);return"function"==typeof u||s.isPresent?n.cloneElement(d,{ref:f}):null};function c(e){return e?.animationName||"none"}u.displayName="Presence";var s=r(3966),d=r(6606),f=r(9114),p=r(2484),h=r(6741),v=r(7437),m="ScrollArea",[y,g]=(0,s.b)(m),[w,b]=y(m),x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:a="hover",dir:l,scrollHideDelay:u=600,...c}=e,[s,d]=n.useState(null),[p,h]=n.useState(null),[m,y]=n.useState(null),[g,b]=n.useState(null),[x,S]=n.useState(null),[E,O]=n.useState(0),[C,j]=n.useState(0),[k,D]=n.useState(!1),[T,P]=n.useState(!1),R=(0,i.e)(t,e=>d(e)),M=(0,f.gm)(l);return(0,v.jsx)(w,{scope:r,type:a,dir:M,scrollHideDelay:u,scrollArea:s,viewport:p,onViewportChange:h,content:m,onContentChange:y,scrollbarX:g,onScrollbarXChange:b,scrollbarXEnabled:k,onScrollbarXEnabledChange:D,scrollbarY:x,onScrollbarYChange:S,scrollbarYEnabled:T,onScrollbarYEnabledChange:P,onCornerWidthChange:O,onCornerHeightChange:j,children:(0,v.jsx)(o.WV.div,{dir:M,...c,ref:R,style:{position:"relative","--radix-scroll-area-corner-width":E+"px","--radix-scroll-area-corner-height":C+"px",...e.style}})})});x.displayName=m;var S="ScrollAreaViewport",E=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:a,nonce:l,...u}=e,c=b(S,r),s=n.useRef(null),d=(0,i.e)(t,s,c.onViewportChange);return(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,v.jsx)(o.WV.div,{"data-radix-scroll-area-viewport":"",...u,ref:d,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,v.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:a})})]})});E.displayName=S;var O="ScrollAreaScrollbar",C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,a=b(O,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:l}=a,u="horizontal"===e.orientation;return n.useEffect(()=>(u?i(!0):l(!0),()=>{u?i(!1):l(!1)}),[u,i,l]),"hover"===a.type?(0,v.jsx)(j,{...o,ref:t,forceMount:r}):"scroll"===a.type?(0,v.jsx)(k,{...o,ref:t,forceMount:r}):"auto"===a.type?(0,v.jsx)(D,{...o,ref:t,forceMount:r}):"always"===a.type?(0,v.jsx)(T,{...o,ref:t}):null});C.displayName=O;var j=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,a=b(O,e.__scopeScrollArea),[i,l]=n.useState(!1);return n.useEffect(()=>{let e=a.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),l(!0)},n=()=>{t=window.setTimeout(()=>l(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[a.scrollArea,a.scrollHideDelay]),(0,v.jsx)(u,{present:r||i,children:(0,v.jsx)(D,{"data-state":i?"visible":"hidden",...o,ref:t})})}),k=n.forwardRef((e,t)=>{var r,o;let{forceMount:a,...i}=e,l=b(O,e.__scopeScrollArea),c="horizontal"===e.orientation,s=$(()=>f("SCROLL_END"),100),[d,f]=(r="hidden",o={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>o[e][t]??e,r));return n.useEffect(()=>{if("idle"===d){let e=window.setTimeout(()=>f("HIDE"),l.scrollHideDelay);return()=>window.clearTimeout(e)}},[d,l.scrollHideDelay,f]),n.useEffect(()=>{let e=l.viewport,t=c?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(f("SCROLL"),s()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[l.viewport,c,f,s]),(0,v.jsx)(u,{present:a||"hidden"!==d,children:(0,v.jsx)(T,{"data-state":"hidden"===d?"hidden":"visible",...i,ref:t,onPointerEnter:(0,h.M)(e.onPointerEnter,()=>f("POINTER_ENTER")),onPointerLeave:(0,h.M)(e.onPointerLeave,()=>f("POINTER_LEAVE"))})})}),D=n.forwardRef((e,t)=>{let r=b(O,e.__scopeScrollArea),{forceMount:o,...a}=e,[i,l]=n.useState(!1),c="horizontal"===e.orientation,s=$(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;l(c?e:t)}},10);return q(r.viewport,s),q(r.content,s),(0,v.jsx)(u,{present:o||i,children:(0,v.jsx)(T,{"data-state":i?"visible":"hidden",...a,ref:t})})}),T=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,a=b(O,e.__scopeScrollArea),i=n.useRef(null),l=n.useRef(0),[u,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),s=H(u.viewport,u.content),d={...o,sizes:u,onSizesChange:c,hasThumb:!!(s>0&&s<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:e=>l.current=e};function f(e,t){return function(e,t,r,n="ltr"){let o=B(r),a=t||o/2,i=r.scrollbar.paddingStart+a,l=r.scrollbar.size-r.scrollbar.paddingEnd-(o-a),u=r.content-r.viewport;return Z([i,l],"ltr"===n?[0,u]:[-1*u,0])(e)}(e,l.current,u,t)}return"horizontal"===r?(0,v.jsx)(P,{...d,ref:t,onThumbPositionChange:()=>{if(a.viewport&&i.current){let e=Y(a.viewport.scrollLeft,u,a.dir);i.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollLeft=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollLeft=f(e,a.dir))}}):"vertical"===r?(0,v.jsx)(R,{...d,ref:t,onThumbPositionChange:()=>{if(a.viewport&&i.current){let e=Y(a.viewport.scrollTop,u);i.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollTop=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollTop=f(e))}}):null}),P=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,l=b(O,e.__scopeScrollArea),[u,c]=n.useState(),s=n.useRef(null),d=(0,i.e)(t,s,l.onScrollbarXChange);return n.useEffect(()=>{s.current&&c(getComputedStyle(s.current))},[s]),(0,v.jsx)(A,{"data-orientation":"horizontal",...a,ref:d,sizes:r,style:{bottom:0,left:"rtl"===l.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===l.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":B(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),n>0&&n<r&&t.preventDefault()}},onResize:()=>{s.current&&l.viewport&&u&&o({content:l.viewport.scrollWidth,viewport:l.viewport.offsetWidth,scrollbar:{size:s.current.clientWidth,paddingStart:z(u.paddingLeft),paddingEnd:z(u.paddingRight)}})}})}),R=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,l=b(O,e.__scopeScrollArea),[u,c]=n.useState(),s=n.useRef(null),d=(0,i.e)(t,s,l.onScrollbarYChange);return n.useEffect(()=>{s.current&&c(getComputedStyle(s.current))},[s]),(0,v.jsx)(A,{"data-orientation":"vertical",...a,ref:d,sizes:r,style:{top:0,right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":B(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),n>0&&n<r&&t.preventDefault()}},onResize:()=>{s.current&&l.viewport&&u&&o({content:l.viewport.scrollHeight,viewport:l.viewport.offsetHeight,scrollbar:{size:s.current.clientHeight,paddingStart:z(u.paddingTop),paddingEnd:z(u.paddingBottom)}})}})}),[M,_]=y(O),A=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:a,hasThumb:l,onThumbChange:u,onThumbPointerUp:c,onThumbPointerDown:s,onThumbPositionChange:f,onDragScroll:p,onWheelScroll:m,onResize:y,...g}=e,w=b(O,r),[x,S]=n.useState(null),E=(0,i.e)(t,e=>S(e)),C=n.useRef(null),j=n.useRef(""),k=w.viewport,D=a.content-a.viewport,T=(0,d.W)(m),P=(0,d.W)(f),R=$(y,10);function _(e){C.current&&p({x:e.clientX-C.current.left,y:e.clientY-C.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;x?.contains(t)&&T(e,D)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[k,x,D,T]),n.useEffect(P,[a,P]),q(x,R),q(w.content,R),(0,v.jsx)(M,{scope:r,scrollbar:x,hasThumb:l,onThumbChange:(0,d.W)(u),onThumbPointerUp:(0,d.W)(c),onThumbPositionChange:P,onThumbPointerDown:(0,d.W)(s),children:(0,v.jsx)(o.WV.div,{...g,ref:E,style:{position:"absolute",...g.style},onPointerDown:(0,h.M)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),C.current=x.getBoundingClientRect(),j.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",w.viewport&&(w.viewport.style.scrollBehavior="auto"),_(e))}),onPointerMove:(0,h.M)(e.onPointerMove,_),onPointerUp:(0,h.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=j.current,w.viewport&&(w.viewport.style.scrollBehavior=""),C.current=null})})})}),N="ScrollAreaThumb",L=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=_(N,e.__scopeScrollArea);return(0,v.jsx)(u,{present:r||o.hasThumb,children:(0,v.jsx)(I,{ref:t,...n})})}),I=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:a,...l}=e,u=b(N,r),c=_(N,r),{onThumbPositionChange:s}=c,d=(0,i.e)(t,e=>c.onThumbChange(e)),f=n.useRef(),p=$(()=>{f.current&&(f.current(),f.current=void 0)},100);return n.useEffect(()=>{let e=u.viewport;if(e){let t=()=>{if(p(),!f.current){let t=U(e,s);f.current=t,s()}};return s(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[u.viewport,p,s]),(0,v.jsx)(o.WV.div,{"data-state":c.hasThumb?"visible":"hidden",...l,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...a},onPointerDownCapture:(0,h.M)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;c.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,h.M)(e.onPointerUp,c.onThumbPointerUp)})});L.displayName=N;var W="ScrollAreaCorner",F=n.forwardRef((e,t)=>{let r=b(W,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,v.jsx)(V,{...e,ref:t}):null});F.displayName=W;var V=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...a}=e,i=b(W,r),[l,u]=n.useState(0),[c,s]=n.useState(0),d=!!(l&&c);return q(i.scrollbarX,()=>{let e=i.scrollbarX?.offsetHeight||0;i.onCornerHeightChange(e),s(e)}),q(i.scrollbarY,()=>{let e=i.scrollbarY?.offsetWidth||0;i.onCornerWidthChange(e),u(e)}),d?(0,v.jsx)(o.WV.div,{...a,ref:t,style:{width:l,height:c,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function z(e){return e?parseInt(e,10):0}function H(e,t){let r=e/t;return isNaN(r)?0:r}function B(e){let t=H(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function Y(e,t,r="ltr"){let n=B(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,a=t.scrollbar.size-o,i=t.content-t.viewport,l=(0,p.u)(e,"ltr"===r?[0,i]:[-1*i,0]);return Z([0,i],[0,a-n])(l)}function Z(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var U=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let a={left:e.scrollLeft,top:e.scrollTop},i=r.left!==a.left,l=r.top!==a.top;(i||l)&&t(),r=a,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function $(e,t){let r=(0,d.W)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function q(e,t){let r=(0,d.W)(t);(0,l.b)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var X=x,G=E,K=F},2217:function(e,t,r){"use strict";let n;r.d(t,{VY:function(){return nv},ZA:function(){return ny},JO:function(){return np},ck:function(){return nw},wU:function(){return nx},eT:function(){return nb},__:function(){return ng},h_:function(){return nh},fC:function(){return ns},$G:function(){return nE},u_:function(){return nS},Z0:function(){return nO},xz:function(){return nd},B4:function(){return nf},l_:function(){return nm}});var o,a,i,l,u,c,s,d,f=r(2265),p=r.t(f,2),h=r(4887),v=r(2484),m=r(6741),y=r(3966),g=r(8575),w=r(7053),b=r(7437),x=r(9114),S=r(6840),E=r(6606),O="dismissableLayer.update",C=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),j=f.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:n,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:i,onDismiss:l,...u}=e,c=f.useContext(C),[d,p]=f.useState(null),h=d?.ownerDocument??globalThis?.document,[,v]=f.useState({}),y=(0,g.e)(t,e=>p(e)),w=Array.from(c.layers),[x]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),j=w.indexOf(x),T=d?w.indexOf(d):-1,P=c.layersWithOutsidePointerEventsDisabled.size>0,R=T>=j,M=function(e,t=globalThis?.document){let r=(0,E.W)(e),n=f.useRef(!1),o=f.useRef(()=>{});return f.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){D("dismissableLayer.pointerDownOutside",r,a,{discrete:!0})},a={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=n,t.addEventListener("click",o.current,{once:!0})):n()}else t.removeEventListener("click",o.current);n.current=!1},a=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...c.branches].some(e=>e.contains(t));!R||r||(o?.(e),i?.(e),e.defaultPrevented||l?.())},h),_=function(e,t=globalThis?.document){let r=(0,E.W)(e),n=f.useRef(!1);return f.useEffect(()=>{let e=e=>{e.target&&!n.current&&D("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[...c.branches].some(e=>e.contains(t))||(a?.(e),i?.(e),e.defaultPrevented||l?.())},h);return!function(e,t=globalThis?.document){let r=(0,E.W)(e);f.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{T!==c.layers.size-1||(n?.(e),!e.defaultPrevented&&l&&(e.preventDefault(),l()))},h),f.useEffect(()=>{if(d)return r&&(0===c.layersWithOutsidePointerEventsDisabled.size&&(s=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(d)),c.layers.add(d),k(),()=>{r&&1===c.layersWithOutsidePointerEventsDisabled.size&&(h.body.style.pointerEvents=s)}},[d,h,r,c]),f.useEffect(()=>()=>{d&&(c.layers.delete(d),c.layersWithOutsidePointerEventsDisabled.delete(d),k())},[d,c]),f.useEffect(()=>{let e=()=>v({});return document.addEventListener(O,e),()=>document.removeEventListener(O,e)},[]),(0,b.jsx)(S.WV.div,{...u,ref:y,style:{pointerEvents:P?R?"auto":"none":void 0,...e.style},onFocusCapture:(0,m.M)(e.onFocusCapture,_.onFocusCapture),onBlurCapture:(0,m.M)(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:(0,m.M)(e.onPointerDownCapture,M.onPointerDownCapture)})});function k(){let e=new CustomEvent(O);document.dispatchEvent(e)}function D(e,t,r,{discrete:n}){let o=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?(0,S.jH)(o,a):o.dispatchEvent(a)}j.displayName="DismissableLayer",f.forwardRef((e,t)=>{let r=f.useContext(C),n=f.useRef(null),o=(0,g.e)(t,n);return f.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,b.jsx)(S.WV.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var T=0;function P(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}var R="focusScope.autoFocusOnMount",M="focusScope.autoFocusOnUnmount",_={bubbles:!1,cancelable:!0},A=f.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...i}=e,[l,u]=f.useState(null),c=(0,E.W)(o),s=(0,E.W)(a),d=f.useRef(null),p=(0,g.e)(t,e=>u(e)),h=f.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;f.useEffect(()=>{if(n){let e=function(e){if(h.paused||!l)return;let t=e.target;l.contains(t)?d.current=t:I(d.current,{select:!0})},t=function(e){if(h.paused||!l)return;let t=e.relatedTarget;null===t||l.contains(t)||I(d.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&I(l)});return l&&r.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,l,h.paused]),f.useEffect(()=>{if(l){W.add(h);let e=document.activeElement;if(!l.contains(e)){let t=new CustomEvent(R,_);l.addEventListener(R,c),l.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(I(n,{select:t}),document.activeElement!==r)return}(N(l).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&I(l))}return()=>{l.removeEventListener(R,c),setTimeout(()=>{let t=new CustomEvent(M,_);l.addEventListener(M,s),l.dispatchEvent(t),t.defaultPrevented||I(e??document.body,{select:!0}),l.removeEventListener(M,s),W.remove(h)},0)}}},[l,c,s,h]);let v=f.useCallback(e=>{if(!r&&!n||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,a]=function(e){let t=N(e);return[L(t,e),L(t.reverse(),e)]}(t);n&&a?e.shiftKey||o!==a?e.shiftKey&&o===n&&(e.preventDefault(),r&&I(a,{select:!0})):(e.preventDefault(),r&&I(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,h.paused]);return(0,b.jsx)(S.WV.div,{tabIndex:-1,...i,ref:p,onKeyDown:v})});function N(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function L(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function I(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}A.displayName="FocusScope";var W=(n=[],{add(e){let t=n[0];e!==t&&t?.pause(),(n=F(n,e)).unshift(e)},remove(e){n=F(n,e),n[0]?.resume()}});function F(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var V=r(1188),z=p["useId".toString()]||(()=>void 0),H=0;function B(e){let[t,r]=f.useState(z());return(0,V.b)(()=>{e||r(e=>e??String(H++))},[e]),e||(t?`radix-${t}`:"")}let Y=["top","right","bottom","left"],Z=Math.min,U=Math.max,$={left:"right",right:"left",bottom:"top",top:"bottom"},q={start:"end",end:"start"};function X(e,t){return"function"==typeof e?e(t):e}function G(e){return e.split("-")[0]}function K(e){return e.split("-")[1]}function J(e){return"x"===e?"y":"x"}function Q(e){return"y"===e?"height":"width"}let ee=new Set(["top","bottom"]);function et(e){return ee.has(G(e))?"y":"x"}function er(e){return e.replace(/start|end/g,e=>q[e])}let en=["left","right"],eo=["right","left"],ea=["top","bottom"],ei=["bottom","top"];function el(e){return e.replace(/left|right|bottom|top/g,e=>$[e])}function eu(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function ec(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function es(e,t,r){let n,{reference:o,floating:a}=e,i=et(t),l=J(et(t)),u=Q(l),c=G(t),s="y"===i,d=o.x+o.width/2-a.width/2,f=o.y+o.height/2-a.height/2,p=o[u]/2-a[u]/2;switch(c){case"top":n={x:d,y:o.y-a.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-a.width,y:f};break;default:n={x:o.x,y:o.y}}switch(K(t)){case"start":n[l]-=p*(r&&s?-1:1);break;case"end":n[l]+=p*(r&&s?-1:1)}return n}let ed=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:a=[],platform:i}=r,l=a.filter(Boolean),u=await (null==i.isRTL?void 0:i.isRTL(t)),c=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=es(c,n,u),f=n,p={},h=0;for(let r=0;r<l.length;r++){let{name:a,fn:v}=l[r],{x:m,y:y,data:g,reset:w}=await v({x:s,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:c,platform:i,elements:{reference:e,floating:t}});s=null!=m?m:s,d=null!=y?y:d,p={...p,[a]:{...p[a],...g}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=es(c,f,u)),r=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function ef(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:a,rects:i,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=X(t,e),h=eu(p),v=l[f?"floating"===d?"reference":"floating":d],m=ec(await a.getClippingRect({element:null==(r=await (null==a.isElement?void 0:a.isElement(v)))||r?v:v.contextElement||await (null==a.getDocumentElement?void 0:a.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===d?{x:n,y:o,width:i.floating.width,height:i.floating.height}:i.reference,g=await (null==a.getOffsetParent?void 0:a.getOffsetParent(l.floating)),w=await (null==a.isElement?void 0:a.isElement(g))&&await (null==a.getScale?void 0:a.getScale(g))||{x:1,y:1},b=ec(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:g,strategy:u}):y);return{top:(m.top-b.top+h.top)/w.y,bottom:(b.bottom-m.bottom+h.bottom)/w.y,left:(m.left-b.left+h.left)/w.x,right:(b.right-m.right+h.right)/w.x}}function ep(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function eh(e){return Y.some(t=>e[t]>=0)}let ev=new Set(["left","top"]);async function em(e,t){let{placement:r,platform:n,elements:o}=e,a=await (null==n.isRTL?void 0:n.isRTL(o.floating)),i=G(r),l=K(r),u="y"===et(r),c=ev.has(i)?-1:1,s=a&&u?-1:1,d=X(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof h&&(p="end"===l?-1*h:h),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}let ey=Math.min,eg=Math.max,ew=Math.round,eb=Math.floor,ex=e=>({x:e,y:e});function eS(){return"undefined"!=typeof window}function eE(e){return ej(e)?(e.nodeName||"").toLowerCase():"#document"}function eO(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eC(e){var t;return null==(t=(ej(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ej(e){return!!eS()&&(e instanceof Node||e instanceof eO(e).Node)}function ek(e){return!!eS()&&(e instanceof Element||e instanceof eO(e).Element)}function eD(e){return!!eS()&&(e instanceof HTMLElement||e instanceof eO(e).HTMLElement)}function eT(e){return!!eS()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof eO(e).ShadowRoot)}let eP=new Set(["inline","contents"]);function eR(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=eH(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!eP.has(o)}let eM=new Set(["table","td","th"]),e_=[":popover-open",":modal"];function eA(e){return e_.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eN=["transform","translate","scale","rotate","perspective"],eL=["transform","translate","scale","rotate","perspective","filter"],eI=["paint","layout","strict","content"];function eW(e){let t=eF(),r=ek(e)?eH(e):e;return eN.some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||eL.some(e=>(r.willChange||"").includes(e))||eI.some(e=>(r.contain||"").includes(e))}function eF(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eV=new Set(["html","body","#document"]);function ez(e){return eV.has(eE(e))}function eH(e){return eO(e).getComputedStyle(e)}function eB(e){return ek(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eY(e){if("html"===eE(e))return e;let t=e.assignedSlot||e.parentNode||eT(e)&&e.host||eC(e);return eT(t)?t.host:t}function eZ(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=eY(t);return ez(r)?t.ownerDocument?t.ownerDocument.body:t.body:eD(r)&&eR(r)?r:e(r)}(e),a=o===(null==(n=e.ownerDocument)?void 0:n.body),i=eO(o);if(a){let e=eU(i);return t.concat(i,i.visualViewport||[],eR(o)?o:[],e&&r?eZ(e):[])}return t.concat(o,eZ(o,[],r))}function eU(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function e$(e){let t=eH(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=eD(e),a=o?e.offsetWidth:r,i=o?e.offsetHeight:n,l=ew(r)!==a||ew(n)!==i;return l&&(r=a,n=i),{width:r,height:n,$:l}}function eq(e){return ek(e)?e:e.contextElement}function eX(e){let t=eq(e);if(!eD(t))return ex(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:a}=e$(t),i=(a?ew(r.width):r.width)/n,l=(a?ew(r.height):r.height)/o;return i&&Number.isFinite(i)||(i=1),l&&Number.isFinite(l)||(l=1),{x:i,y:l}}let eG=ex(0);function eK(e){let t=eO(e);return eF()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eG}function eJ(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let a=e.getBoundingClientRect(),i=eq(e),l=ex(1);t&&(n?ek(n)&&(l=eX(n)):l=eX(e));let u=(void 0===(o=r)&&(o=!1),n&&(!o||n===eO(i))&&o)?eK(i):ex(0),c=(a.left+u.x)/l.x,s=(a.top+u.y)/l.y,d=a.width/l.x,f=a.height/l.y;if(i){let e=eO(i),t=n&&ek(n)?eO(n):n,r=e,o=eU(r);for(;o&&n&&t!==r;){let e=eX(o),t=o.getBoundingClientRect(),n=eH(o),a=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=a,s+=i,o=eU(r=eO(o))}}return ec({width:d,height:f,x:c,y:s})}function eQ(e,t){let r=eB(e).scrollLeft;return t?t.left+r:eJ(eC(e)).left+r}function e0(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:eQ(e,n)),y:n.top+t.scrollTop}}let e1=new Set(["absolute","fixed"]);function e2(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=eO(e),n=eC(e),o=r.visualViewport,a=n.clientWidth,i=n.clientHeight,l=0,u=0;if(o){a=o.width,i=o.height;let e=eF();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:a,height:i,x:l,y:u}}(e,r);else if("document"===t)n=function(e){let t=eC(e),r=eB(e),n=e.ownerDocument.body,o=eg(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),a=eg(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),i=-r.scrollLeft+eQ(e),l=-r.scrollTop;return"rtl"===eH(n).direction&&(i+=eg(t.clientWidth,n.clientWidth)-o),{width:o,height:a,x:i,y:l}}(eC(e));else if(ek(t))n=function(e,t){let r=eJ(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,a=eD(e)?eX(e):ex(1),i=e.clientWidth*a.x;return{width:i,height:e.clientHeight*a.y,x:o*a.x,y:n*a.y}}(t,r);else{let r=eK(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return ec(n)}function e5(e){return"static"===eH(e).position}function e4(e,t){if(!eD(e)||"fixed"===eH(e).position)return null;if(t)return t(e);let r=e.offsetParent;return eC(e)===r&&(r=r.ownerDocument.body),r}function e6(e,t){var r;let n=eO(e);if(eA(e))return n;if(!eD(e)){let t=eY(e);for(;t&&!ez(t);){if(ek(t)&&!e5(t))return t;t=eY(t)}return n}let o=e4(e,t);for(;o&&(r=o,eM.has(eE(r)))&&e5(o);)o=e4(o,t);return o&&ez(o)&&e5(o)&&!eW(o)?n:o||function(e){let t=eY(e);for(;eD(t)&&!ez(t);){if(eW(t))return t;if(eA(t))break;t=eY(t)}return null}(e)||n}let e3=async function(e){let t=this.getOffsetParent||e6,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=eD(t),o=eC(t),a="fixed"===r,i=eJ(e,!0,a,t),l={scrollLeft:0,scrollTop:0},u=ex(0);if(n||!n&&!a){if(("body"!==eE(t)||eR(o))&&(l=eB(t)),n){let e=eJ(t,!0,a,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eQ(o))}a&&!n&&o&&(u.x=eQ(o));let c=!o||n||a?ex(0):e0(o,l);return{x:i.left+l.scrollLeft-u.x-c.x,y:i.top+l.scrollTop-u.y-c.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},e8={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,a="fixed"===o,i=eC(n),l=!!t&&eA(t.floating);if(n===i||l&&a)return r;let u={scrollLeft:0,scrollTop:0},c=ex(1),s=ex(0),d=eD(n);if((d||!d&&!a)&&(("body"!==eE(n)||eR(i))&&(u=eB(n)),eD(n))){let e=eJ(n);c=eX(n),s.x=e.x+n.clientLeft,s.y=e.y+n.clientTop}let f=!i||d||a?ex(0):e0(i,u,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:r.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:eC,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,a=[..."clippingAncestors"===r?eA(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=eZ(e,[],!1).filter(e=>ek(e)&&"body"!==eE(e)),o=null,a="fixed"===eH(e).position,i=a?eY(e):e;for(;ek(i)&&!ez(i);){let t=eH(i),r=eW(i);r||"fixed"!==t.position||(o=null),(a?!r&&!o:!r&&"static"===t.position&&!!o&&e1.has(o.position)||eR(i)&&!r&&function e(t,r){let n=eY(t);return!(n===r||!ek(n)||ez(n))&&("fixed"===eH(n).position||e(n,r))}(e,i))?n=n.filter(e=>e!==i):o=t,i=eY(i)}return t.set(e,n),n}(t,this._c):[].concat(r),n],i=a[0],l=a.reduce((e,r)=>{let n=e2(t,r,o);return e.top=eg(n.top,e.top),e.right=ey(n.right,e.right),e.bottom=ey(n.bottom,e.bottom),e.left=eg(n.left,e.left),e},e2(t,i,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:e6,getElementRects:e3,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=e$(e);return{width:t,height:r}},getScale:eX,isElement:ek,isRTL:function(e){return"rtl"===eH(e).direction}};function e7(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e9=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:a,platform:i,elements:l,middlewareData:u}=t,{element:c,padding:s=0}=X(e,t)||{};if(null==c)return{};let d=eu(s),f={x:r,y:n},p=J(et(o)),h=Q(p),v=await i.getDimensions(c),m="y"===p,y=m?"clientHeight":"clientWidth",g=a.reference[h]+a.reference[p]-f[p]-a.floating[h],w=f[p]-a.reference[p],b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(c)),x=b?b[y]:0;x&&await (null==i.isElement?void 0:i.isElement(b))||(x=l.floating[y]||a.floating[h]);let S=x/2-v[h]/2-1,E=Z(d[m?"top":"left"],S),O=Z(d[m?"bottom":"right"],S),C=x-v[h]-O,j=x/2-v[h]/2+(g/2-w/2),k=U(E,Z(j,C)),D=!u.arrow&&null!=K(o)&&j!==k&&a.reference[h]/2-(j<E?E:O)-v[h]/2<0,T=D?j<E?j-E:j-C:0;return{[p]:f[p]+T,data:{[p]:k,centerOffset:j-k-T,...D&&{alignmentOffset:T}},reset:D}}}),te=(e,t,r)=>{let n=new Map,o={platform:e8,...r},a={...o.platform,_c:n};return ed(e,t,{...o,platform:a})};var tt="undefined"!=typeof document?f.useLayoutEffect:function(){};function tr(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!tr(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!tr(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function tn(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function to(e,t){let r=tn(e);return Math.round(t*r)/r}function ta(e){let t=f.useRef(e);return tt(()=>{t.current=e}),t}let ti=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?e9({element:r.current,padding:n}).fn(t):{}:r?e9({element:r,padding:n}).fn(t):{}}}),tl=(e,t)=>{var r;return{...(void 0===(r=e)&&(r=0),{name:"offset",options:r,async fn(e){var t,n;let{x:o,y:a,placement:i,middlewareData:l}=e,u=await em(e,r);return i===(null==(t=l.offset)?void 0:t.placement)&&null!=(n=l.arrow)&&n.alignmentOffset?{}:{x:o+u.x,y:a+u.y,data:{...u,placement:i}}}}),options:[e,t]}},tu=(e,t)=>{var r;return{...(void 0===(r=e)&&(r={}),{name:"shift",options:r,async fn(e){let{x:t,y:n,placement:o}=e,{mainAxis:a=!0,crossAxis:i=!1,limiter:l={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...u}=X(r,e),c={x:t,y:n},s=await ef(e,u),d=et(G(o)),f=J(d),p=c[f],h=c[d];if(a){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=p+s[e],n=p-s[t];p=U(r,Z(p,n))}if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",r=h+s[e],n=h-s[t];h=U(r,Z(h,n))}let v=l.fn({...e,[f]:p,[d]:h});return{...v,data:{x:v.x-t,y:v.y-n,enabled:{[f]:a,[d]:i}}}}}),options:[e,t]}},tc=(e,t)=>{var r;return{...(void 0===(r=e)&&(r={}),{options:r,fn(e){let{x:t,y:n,placement:o,rects:a,middlewareData:i}=e,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=X(r,e),s={x:t,y:n},d=et(o),f=J(d),p=s[f],h=s[d],v=X(l,e),m="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(u){let e="y"===f?"height":"width",t=a.reference[f]-a.floating[e]+m.mainAxis,r=a.reference[f]+a.reference[e]-m.mainAxis;p<t?p=t:p>r&&(p=r)}if(c){var y,g;let e="y"===f?"width":"height",t=ev.has(G(o)),r=a.reference[d]-a.floating[e]+(t&&(null==(y=i.offset)?void 0:y[d])||0)+(t?0:m.crossAxis),n=a.reference[d]+a.reference[e]+(t?0:(null==(g=i.offset)?void 0:g[d])||0)-(t?m.crossAxis:0);h<r?h=r:h>n&&(h=n)}return{[f]:p,[d]:h}}}),options:[e,t]}},ts=(e,t)=>{var r;return{...(void 0===(r=e)&&(r={}),{name:"flip",options:r,async fn(e){var t,n,o,a,i;let{placement:l,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=e,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:v,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:g=!0,...w}=X(r,e);if(null!=(t=u.arrow)&&t.alignmentOffset)return{};let b=G(l),x=et(s),S=G(s)===s,E=await (null==d.isRTL?void 0:d.isRTL(f.floating)),O=v||(S||!g?[el(s)]:function(e){let t=el(e);return[er(e),t,er(t)]}(s)),C="none"!==y;!v&&C&&O.push(...function(e,t,r,n){let o=K(e),a=function(e,t,r){switch(e){case"top":case"bottom":if(r)return t?eo:en;return t?en:eo;case"left":case"right":return t?ea:ei;default:return[]}}(G(e),"start"===r,n);return o&&(a=a.map(e=>e+"-"+o),t&&(a=a.concat(a.map(er)))),a}(s,g,y,E));let j=[s,...O],k=await ef(e,w),D=[],T=(null==(n=u.flip)?void 0:n.overflows)||[];if(p&&D.push(k[b]),h){let e=function(e,t,r){void 0===r&&(r=!1);let n=K(e),o=J(et(e)),a=Q(o),i="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=el(i)),[i,el(i)]}(l,c,E);D.push(k[e[0]],k[e[1]])}if(T=[...T,{placement:l,overflows:D}],!D.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=j[e];if(t&&(!("alignment"===h&&x!==et(t))||T.every(e=>e.overflows[0]>0&&et(e.placement)===x)))return{data:{index:e,overflows:T},reset:{placement:t}};let r=null==(a=T.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:a.placement;if(!r)switch(m){case"bestFit":{let e=null==(i=T.filter(e=>{if(C){let t=et(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(r=e);break}case"initialPlacement":r=s}if(l!==r)return{reset:{placement:r}}}return{}}}),options:[e,t]}},td=(e,t)=>{var r;return{...(void 0===(r=e)&&(r={}),{name:"size",options:r,async fn(e){var t,n;let o,a;let{placement:i,rects:l,platform:u,elements:c}=e,{apply:s=()=>{},...d}=X(r,e),f=await ef(e,d),p=G(i),h=K(i),v="y"===et(i),{width:m,height:y}=l.floating;"top"===p||"bottom"===p?(o=p,a=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(a=p,o="end"===h?"top":"bottom");let g=y-f.top-f.bottom,w=m-f.left-f.right,b=Z(y-f[o],g),x=Z(m-f[a],w),S=!e.middlewareData.shift,E=b,O=x;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(O=w),null!=(n=e.middlewareData.shift)&&n.enabled.y&&(E=g),S&&!h){let e=U(f.left,0),t=U(f.right,0),r=U(f.top,0),n=U(f.bottom,0);v?O=m-2*(0!==e||0!==t?e+t:U(f.left,f.right)):E=y-2*(0!==r||0!==n?r+n:U(f.top,f.bottom))}await s({...e,availableWidth:O,availableHeight:E});let C=await u.getDimensions(c.floating);return m!==C.width||y!==C.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},tf=(e,t)=>{var r;return{...(void 0===(r=e)&&(r={}),{name:"hide",options:r,async fn(e){let{rects:t}=e,{strategy:n="referenceHidden",...o}=X(r,e);switch(n){case"referenceHidden":{let r=ep(await ef(e,{...o,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:r,referenceHidden:eh(r)}}}case"escaped":{let r=ep(await ef(e,{...o,altBoundary:!0}),t.floating);return{data:{escapedOffsets:r,escaped:eh(r)}}}default:return{}}}}),options:[e,t]}},tp=(e,t)=>({...ti(e),options:[e,t]});var th=f.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...a}=e;return(0,b.jsx)(S.WV.svg,{...a,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,b.jsx)("polygon",{points:"0,0 30,0 15,10"})})});th.displayName="Arrow";var tv="Popper",[tm,ty]=(0,y.b)(tv),[tg,tw]=tm(tv),tb=e=>{let{__scopePopper:t,children:r}=e,[n,o]=f.useState(null);return(0,b.jsx)(tg,{scope:t,anchor:n,onAnchorChange:o,children:r})};tb.displayName=tv;var tx="PopperAnchor",tS=f.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...o}=e,a=tw(tx,r),i=f.useRef(null),l=(0,g.e)(t,i);return f.useEffect(()=>{a.onAnchorChange(n?.current||i.current)}),n?null:(0,b.jsx)(S.WV.div,{...o,ref:l})});tS.displayName=tx;var tE="PopperContent",[tO,tC]=tm(tE),tj=f.forwardRef((e,t)=>{let{__scopePopper:r,side:n="bottom",sideOffset:o=0,align:a="center",alignOffset:i=0,arrowPadding:l=0,avoidCollisions:u=!0,collisionBoundary:c=[],collisionPadding:s=0,sticky:d="partial",hideWhenDetached:p=!1,updatePositionStrategy:v="optimized",onPlaced:m,...y}=e,w=tw(tE,r),[x,O]=f.useState(null),C=(0,g.e)(t,e=>O(e)),[j,k]=f.useState(null),D=function(e){let[t,r]=f.useState(void 0);return(0,V.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(j),T=D?.width??0,P=D?.height??0,R="number"==typeof s?s:{top:0,right:0,bottom:0,left:0,...s},M=Array.isArray(c)?c:[c],_=M.length>0,A={padding:R,boundary:M.filter(tP),altBoundary:_},{refs:N,floatingStyles:L,placement:I,isPositioned:W,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:o,elements:{reference:a,floating:i}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[s,d]=f.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,v]=f.useState(n);tr(p,n)||v(n);let[m,y]=f.useState(null),[g,w]=f.useState(null),b=f.useCallback(e=>{e!==O.current&&(O.current=e,y(e))},[]),x=f.useCallback(e=>{e!==C.current&&(C.current=e,w(e))},[]),S=a||m,E=i||g,O=f.useRef(null),C=f.useRef(null),j=f.useRef(s),k=null!=u,D=ta(u),T=ta(o),P=ta(c),R=f.useCallback(()=>{if(!O.current||!C.current)return;let e={placement:t,strategy:r,middleware:p};T.current&&(e.platform=T.current),te(O.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};M.current&&!tr(j.current,t)&&(j.current=t,h.flushSync(()=>{d(t)}))})},[p,t,r,T,P]);tt(()=>{!1===c&&j.current.isPositioned&&(j.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let M=f.useRef(!1);tt(()=>(M.current=!0,()=>{M.current=!1}),[]),tt(()=>{if(S&&(O.current=S),E&&(C.current=E),S&&E){if(D.current)return D.current(S,E,R);R()}},[S,E,R,D,k]);let _=f.useMemo(()=>({reference:O,floating:C,setReference:b,setFloating:x}),[b,x]),A=f.useMemo(()=>({reference:S,floating:E}),[S,E]),N=f.useMemo(()=>{let e={position:r,left:0,top:0};if(!A.floating)return e;let t=to(A.floating,s.x),n=to(A.floating,s.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...tn(A.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,l,A.floating,s.x,s.y]);return f.useMemo(()=>({...s,update:R,refs:_,elements:A,floatingStyles:N}),[s,R,_,A,N])}({strategy:"fixed",placement:n+("center"!==a?"-"+a:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:a=!0,ancestorResize:i=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=n,s=eq(e),d=a||i?[...s?eZ(s):[],...eZ(t)]:[];d.forEach(e=>{a&&e.addEventListener("scroll",r,{passive:!0}),i&&e.addEventListener("resize",r)});let f=s&&u?function(e,t){let r,n=null,o=eC(e);function a(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function i(l,u){void 0===l&&(l=!1),void 0===u&&(u=1),a();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(l||t(),!f||!p)return;let h=eb(d),v=eb(o.clientWidth-(s+f)),m={rootMargin:-h+"px "+-v+"px "+-eb(o.clientHeight-(d+p))+"px "+-eb(s)+"px",threshold:eg(0,ey(1,u))||1},y=!0;function g(t){let n=t[0].intersectionRatio;if(n!==u){if(!y)return i();n?i(!1,n):r=setTimeout(()=>{i(!1,1e-7)},1e3)}1!==n||e7(c,e.getBoundingClientRect())||i(),y=!1}try{n=new IntersectionObserver(g,{...m,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(g,m)}n.observe(e)}(!0),a}(s,r):null,p=-1,h=null;l&&(h=new ResizeObserver(e=>{let[n]=e;n&&n.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),r()}),s&&!c&&h.observe(s),h.observe(t));let v=c?eJ(e):null;return c&&function t(){let n=eJ(e);v&&!e7(v,n)&&r(),v=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;d.forEach(e=>{a&&e.removeEventListener("scroll",r),i&&e.removeEventListener("resize",r)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===v}),elements:{reference:w.anchor},middleware:[tl({mainAxis:o+P,alignmentAxis:i}),u&&tu({mainAxis:!0,crossAxis:!1,limiter:"partial"===d?tc():void 0,...A}),u&&ts({...A}),td({...A,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:a}=t.reference,i=e.floating.style;i.setProperty("--radix-popper-available-width",`${r}px`),i.setProperty("--radix-popper-available-height",`${n}px`),i.setProperty("--radix-popper-anchor-width",`${o}px`),i.setProperty("--radix-popper-anchor-height",`${a}px`)}}),j&&tp({element:j,padding:l}),tR({arrowWidth:T,arrowHeight:P}),p&&tf({strategy:"referenceHidden",...A})]}),[z,H]=tM(I),B=(0,E.W)(m);(0,V.b)(()=>{W&&B?.()},[W,B]);let Y=F.arrow?.x,Z=F.arrow?.y,U=F.arrow?.centerOffset!==0,[$,q]=f.useState();return(0,V.b)(()=>{x&&q(window.getComputedStyle(x).zIndex)},[x]),(0,b.jsx)("div",{ref:N.setFloating,"data-radix-popper-content-wrapper":"",style:{...L,transform:W?L.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:$,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,b.jsx)(tO,{scope:r,placedSide:z,onArrowChange:k,arrowX:Y,arrowY:Z,shouldHideArrow:U,children:(0,b.jsx)(S.WV.div,{"data-side":z,"data-align":H,...y,ref:C,style:{...y.style,animation:W?void 0:"none"}})})})});tj.displayName=tE;var tk="PopperArrow",tD={top:"bottom",right:"left",bottom:"top",left:"right"},tT=f.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=tC(tk,r),a=tD[o.placedSide];return(0,b.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,b.jsx)(th,{...n,ref:t,style:{...n.style,display:"block"}})})});function tP(e){return null!==e}tT.displayName=tk;var tR=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,a=o.arrow?.centerOffset!==0,i=a?0:e.arrowWidth,l=a?0:e.arrowHeight,[u,c]=tM(r),s={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+i/2,f=(o.arrow?.y??0)+l/2,p="",h="";return"bottom"===u?(p=a?s:`${d}px`,h=`${-l}px`):"top"===u?(p=a?s:`${d}px`,h=`${n.floating.height+l}px`):"right"===u?(p=`${-l}px`,h=a?s:`${f}px`):"left"===u&&(p=`${n.floating.width+l}px`,h=a?s:`${f}px`),{data:{x:p,y:h}}}});function tM(e){let[t,r="center"]=e.split("-");return[t,r]}var t_=f.forwardRef((e,t)=>{let{container:r,...n}=e,[o,a]=f.useState(!1);(0,V.b)(()=>a(!0),[]);let i=r||o&&globalThis?.document?.body;return i?h.createPortal((0,b.jsx)(S.WV.div,{...n,ref:t}),i):null});function tA({prop:e,defaultProp:t,onChange:r=()=>{}}){let[n,o]=function({defaultProp:e,onChange:t}){let r=f.useState(e),[n]=r,o=f.useRef(n),a=(0,E.W)(t);return f.useEffect(()=>{o.current!==n&&(a(n),o.current=n)},[n,o,a]),r}({defaultProp:t,onChange:r}),a=void 0!==e,i=a?e:n,l=(0,E.W)(r);return[i,f.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&l(r)}else o(t)},[a,e,o,l])]}t_.displayName="Portal";var tN=f.forwardRef((e,t)=>(0,b.jsx)(S.WV.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));tN.displayName="VisuallyHidden";var tL=new WeakMap,tI=new WeakMap,tW={},tF=0,tV=function(e){return e&&(e.host||tV(e.parentNode))},tz=function(e,t,r,n){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=tV(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tW[r]||(tW[r]=new WeakMap);var a=tW[r],i=[],l=new Set,u=new Set(o),c=function(e){!e||l.has(e)||(l.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(l.has(e))s(e);else try{var t=e.getAttribute(n),o=null!==t&&"false"!==t,u=(tL.get(e)||0)+1,c=(a.get(e)||0)+1;tL.set(e,u),a.set(e,c),i.push(e),1===u&&o&&tI.set(e,!0),1===c&&e.setAttribute(r,"true"),o||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),l.clear(),tF++,function(){i.forEach(function(e){var t=tL.get(e)-1,o=a.get(e)-1;tL.set(e,t),a.set(e,o),t||(tI.has(e)||e.removeAttribute(n),tI.delete(e)),o||e.removeAttribute(r)}),--tF||(tL=new WeakMap,tL=new WeakMap,tI=new WeakMap,tW={})}},tH=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(n.push.apply(n,Array.from(o.querySelectorAll("[aria-live], script"))),tz(n,o,r,"aria-hidden")):function(){return null}},tB=function(){return(tB=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tY(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}"function"==typeof SuppressedError&&SuppressedError;var tZ="right-scroll-bar-position",tU="width-before-scroll-bar";function t$(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tq="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,tX=new WeakMap,tG=(void 0===o&&(o={}),(void 0===a&&(a=function(e){return e}),i=[],l=!1,u={read:function(){if(l)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return i.length?i[i.length-1]:null},useMedium:function(e){var t=a(e,l);return i.push(t),function(){i=i.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(l=!0;i.length;){var t=i;i=[],t.forEach(e)}i={push:function(t){return e(t)},filter:function(){return i}}},assignMedium:function(e){l=!0;var t=[];if(i.length){var r=i;i=[],r.forEach(e),t=i}var n=function(){var r=t;t=[],r.forEach(e)},o=function(){return Promise.resolve().then(n)};o(),i={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),i}}}}).options=tB({async:!0,ssr:!1},o),u),tK=function(){},tJ=f.forwardRef(function(e,t){var r,n,o,a,i=f.useRef(null),l=f.useState({onScrollCapture:tK,onWheelCapture:tK,onTouchMoveCapture:tK}),u=l[0],c=l[1],s=e.forwardProps,d=e.children,p=e.className,h=e.removeScrollBar,v=e.enabled,m=e.shards,y=e.sideCar,g=e.noIsolation,w=e.inert,b=e.allowPinchZoom,x=e.as,S=e.gapMode,E=tY(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),O=(r=[i,t],n=function(e){return r.forEach(function(t){return t$(t,e)})},(o=(0,f.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,a=o.facade,tq(function(){var e=tX.get(a);if(e){var t=new Set(e),n=new Set(r),o=a.current;t.forEach(function(e){n.has(e)||t$(e,null)}),n.forEach(function(e){t.has(e)||t$(e,o)})}tX.set(a,r)},[r]),a),C=tB(tB({},E),u);return f.createElement(f.Fragment,null,v&&f.createElement(y,{sideCar:tG,removeScrollBar:h,shards:m,noIsolation:g,inert:w,setCallbacks:c,allowPinchZoom:!!b,lockRef:i,gapMode:S}),s?f.cloneElement(f.Children.only(d),tB(tB({},C),{ref:O})):f.createElement(void 0===x?"div":x,tB({},C,{className:p,ref:O}),d))});tJ.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tJ.classNames={fullWidth:tU,zeroRight:tZ};var tQ=function(e){var t=e.sideCar,r=tY(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return f.createElement(n,tB({},r))};tQ.isSideCarExport=!0;var t0=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=d||r.nc;return t&&e.setAttribute("nonce",t),e}())){var o,a;(o=t).styleSheet?o.styleSheet.cssText=n:o.appendChild(document.createTextNode(n)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},t1=function(){var e=t0();return function(t,r){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},t2=function(){var e=t1();return function(t){return e(t.styles,t.dynamic),null}},t5={left:0,top:0,right:0,gap:0},t4=function(e){return parseInt(e||"",10)||0},t6=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[t4(r),t4(n),t4(o)]},t3=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t5;var t=t6(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},t8=t2(),t7="data-scroll-locked",t9=function(e,t,r,n){var o=e.left,a=e.top,i=e.right,l=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(l,"px ").concat(n,";\n  }\n  body[").concat(t7,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(l,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tZ," {\n    right: ").concat(l,"px ").concat(n,";\n  }\n  \n  .").concat(tU," {\n    margin-right: ").concat(l,"px ").concat(n,";\n  }\n  \n  .").concat(tZ," .").concat(tZ," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(tU," .").concat(tU," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(t7,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},re=function(){var e=parseInt(document.body.getAttribute(t7)||"0",10);return isFinite(e)?e:0},rt=function(){f.useEffect(function(){return document.body.setAttribute(t7,(re()+1).toString()),function(){var e=re()-1;e<=0?document.body.removeAttribute(t7):document.body.setAttribute(t7,e.toString())}},[])},rr=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;rt();var a=f.useMemo(function(){return t3(o)},[o]);return f.createElement(t8,{styles:t9(a,!t,o,r?"":"!important")})},rn=!1;if("undefined"!=typeof window)try{var ro=Object.defineProperty({},"passive",{get:function(){return rn=!0,!0}});window.addEventListener("test",ro,ro),window.removeEventListener("test",ro,ro)}catch(e){rn=!1}var ra=!!rn&&{passive:!1},ri=function(e,t){var r=window.getComputedStyle(e);return"hidden"!==r[t]&&!(r.overflowY===r.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===r[t])},rl=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),ru(e,n)){var o=rc(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},ru=function(e,t){return"v"===e?ri(t,"overflowY"):ri(t,"overflowX")},rc=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},rs=function(e,t,r,n,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),l=i*n,u=r.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{var h=rc(e,u),v=h[0],m=h[1]-h[2]-i*v;(v||m)&&ru(e,u)&&(f+=m,p+=v),u instanceof ShadowRoot?u=u.host:u=u.parentNode}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},rd=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},rf=function(e){return[e.deltaX,e.deltaY]},rp=function(e){return e&&"current"in e?e.current:e},rh=0,rv=[],rm=(c=function(e){var t=f.useRef([]),r=f.useRef([0,0]),n=f.useRef(),o=f.useState(rh++)[0],a=f.useState(t2)[0],i=f.useRef(e);f.useEffect(function(){i.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(rp),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=f.useCallback(function(e,t){if("touches"in e&&2===e.touches.length)return!i.current.allowPinchZoom;var o,a=rd(e),l=r.current,u="deltaX"in e?e.deltaX:l[0]-a[0],c="deltaY"in e?e.deltaY:l[1]-a[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=rl(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=rl(d,s)),!f)return!1;if(!n.current&&"changedTouches"in e&&(u||c)&&(n.current=o),!o)return!0;var p=n.current||o;return rs(p,t,e,"h"===p?u:c,!0)},[]),u=f.useCallback(function(e){if(rv.length&&rv[rv.length-1]===a){var r="deltaY"in e?rf(e):rd(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(i.current.shards||[]).map(rp).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=f.useCallback(function(e,r,n,o){var a={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),s=f.useCallback(function(e){r.current=rd(e),n.current=void 0},[]),d=f.useCallback(function(t){c(t.type,rf(t),t.target,l(t,e.lockRef.current))},[]),p=f.useCallback(function(t){c(t.type,rd(t),t.target,l(t,e.lockRef.current))},[]);f.useEffect(function(){return rv.push(a),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",u,ra),document.addEventListener("touchmove",u,ra),document.addEventListener("touchstart",s,ra),function(){rv=rv.filter(function(e){return e!==a}),document.removeEventListener("wheel",u,ra),document.removeEventListener("touchmove",u,ra),document.removeEventListener("touchstart",s,ra)}},[]);var h=e.removeScrollBar,v=e.inert;return f.createElement(f.Fragment,null,v?f.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?f.createElement(rr,{gapMode:e.gapMode}):null)},tG.useMedium(c),tQ),ry=f.forwardRef(function(e,t){return f.createElement(tJ,tB({},e,{ref:t,sideCar:rm}))});ry.classNames=tJ.classNames;var rg=[" ","Enter","ArrowUp","ArrowDown"],rw=[" ","Enter"],rb="Select",[rx,rS,rE]=function(e){let t=e+"CollectionProvider",[r,n]=(0,y.b)(t),[o,a]=r(t,{collectionRef:{current:null},itemMap:new Map}),i=e=>{let{scope:t,children:r}=e,n=f.useRef(null),a=f.useRef(new Map).current;return(0,b.jsx)(o,{scope:t,itemMap:a,collectionRef:n,children:r})};i.displayName=t;let l=e+"CollectionSlot",u=f.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=a(l,r),i=(0,g.e)(t,o.collectionRef);return(0,b.jsx)(w.g7,{ref:i,children:n})});u.displayName=l;let c=e+"CollectionItemSlot",s="data-radix-collection-item",d=f.forwardRef((e,t)=>{let{scope:r,children:n,...o}=e,i=f.useRef(null),l=(0,g.e)(t,i),u=a(c,r);return f.useEffect(()=>(u.itemMap.set(i,{ref:i,...o}),()=>void u.itemMap.delete(i))),(0,b.jsx)(w.g7,{[s]:"",ref:l,children:n})});return d.displayName=c,[{Provider:i,Slot:u,ItemSlot:d},function(t){let r=a(e+"CollectionConsumer",t);return f.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${s}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}(rb),[rO,rC]=(0,y.b)(rb,[rE,ty]),rj=ty(),[rk,rD]=rO(rb),[rT,rP]=rO(rb),rR=e=>{let{__scopeSelect:t,children:r,open:n,defaultOpen:o,onOpenChange:a,value:i,defaultValue:l,onValueChange:u,dir:c,name:s,autoComplete:d,disabled:p,required:h}=e,v=rj(t),[m,y]=f.useState(null),[g,w]=f.useState(null),[S,E]=f.useState(!1),O=(0,x.gm)(c),[C=!1,j]=tA({prop:n,defaultProp:o,onChange:a}),[k,D]=tA({prop:i,defaultProp:l,onChange:u}),T=f.useRef(null),P=!m||!!m.closest("form"),[R,M]=f.useState(new Set),_=Array.from(R).map(e=>e.props.value).join(";");return(0,b.jsx)(tb,{...v,children:(0,b.jsxs)(rk,{required:h,scope:t,trigger:m,onTriggerChange:y,valueNode:g,onValueNodeChange:w,valueNodeHasChildren:S,onValueNodeHasChildrenChange:E,contentId:B(),value:k,onValueChange:D,open:C,onOpenChange:j,dir:O,triggerPointerDownPosRef:T,disabled:p,children:[(0,b.jsx)(rx.Provider,{scope:t,children:(0,b.jsx)(rT,{scope:e.__scopeSelect,onNativeOptionAdd:f.useCallback(e=>{M(t=>new Set(t).add(e))},[]),onNativeOptionRemove:f.useCallback(e=>{M(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),P?(0,b.jsxs)(nl,{"aria-hidden":!0,required:h,tabIndex:-1,name:s,autoComplete:d,value:k,onChange:e=>D(e.target.value),disabled:p,children:[void 0===k?(0,b.jsx)("option",{value:""}):null,Array.from(R)]},_):null]})})};rR.displayName=rb;var rM="SelectTrigger",r_=f.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:n=!1,...o}=e,a=rj(r),i=rD(rM,r),l=i.disabled||n,u=(0,g.e)(t,i.onTriggerChange),c=rS(r),[s,d,f]=nu(e=>{let t=c().filter(e=>!e.disabled),r=t.find(e=>e.value===i.value),n=nc(t,e,r);void 0!==n&&i.onValueChange(n.value)}),p=()=>{l||(i.onOpenChange(!0),f())};return(0,b.jsx)(tS,{asChild:!0,...a,children:(0,b.jsx)(S.WV.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:l,"data-disabled":l?"":void 0,"data-placeholder":ni(i.value)?"":void 0,...o,ref:u,onClick:(0,m.M)(o.onClick,e=>{e.currentTarget.focus()}),onPointerDown:(0,m.M)(o.onPointerDown,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&(p(),i.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)},e.preventDefault())}),onKeyDown:(0,m.M)(o.onKeyDown,e=>{let t=""!==s.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||d(e.key),(!t||" "!==e.key)&&rg.includes(e.key)&&(p(),e.preventDefault())})})})});r_.displayName=rM;var rA="SelectValue",rN=f.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:a,placeholder:i="",...l}=e,u=rD(rA,r),{onValueNodeHasChildrenChange:c}=u,s=void 0!==a,d=(0,g.e)(t,u.onValueNodeChange);return(0,V.b)(()=>{c(s)},[c,s]),(0,b.jsx)(S.WV.span,{...l,ref:d,style:{pointerEvents:"none"},children:ni(u.value)?(0,b.jsx)(b.Fragment,{children:i}):a})});rN.displayName=rA;var rL=f.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,b.jsx)(S.WV.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});rL.displayName="SelectIcon";var rI=e=>(0,b.jsx)(t_,{asChild:!0,...e});rI.displayName="SelectPortal";var rW="SelectContent",rF=f.forwardRef((e,t)=>{let r=rD(rW,e.__scopeSelect),[n,o]=f.useState();return((0,V.b)(()=>{o(new DocumentFragment)},[]),r.open)?(0,b.jsx)(rH,{...e,ref:t}):n?h.createPortal((0,b.jsx)(rV,{scope:e.__scopeSelect,children:(0,b.jsx)(rx.Slot,{scope:e.__scopeSelect,children:(0,b.jsx)("div",{children:e.children})})}),n):null});rF.displayName=rW;var[rV,rz]=rO(rW),rH=f.forwardRef((e,t)=>{let{__scopeSelect:r,position:n="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:a,onPointerDownOutside:i,side:l,sideOffset:u,align:c,alignOffset:s,arrowPadding:d,collisionBoundary:p,collisionPadding:h,sticky:v,hideWhenDetached:y,avoidCollisions:x,...S}=e,E=rD(rW,r),[O,C]=f.useState(null),[k,D]=f.useState(null),R=(0,g.e)(t,e=>C(e)),[M,_]=f.useState(null),[N,L]=f.useState(null),I=rS(r),[W,F]=f.useState(!1),V=f.useRef(!1);f.useEffect(()=>{if(O)return tH(O)},[O]),f.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??P()),document.body.insertAdjacentElement("beforeend",e[1]??P()),T++,()=>{1===T&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),T--}},[]);let z=f.useCallback(e=>{let[t,...r]=I().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(r?.scrollIntoView({block:"nearest"}),r===t&&k&&(k.scrollTop=0),r===n&&k&&(k.scrollTop=k.scrollHeight),r?.focus(),document.activeElement!==o))return},[I,k]),H=f.useCallback(()=>z([M,O]),[z,M,O]);f.useEffect(()=>{W&&H()},[W,H]);let{onOpenChange:B,triggerPointerDownPosRef:Y}=E;f.useEffect(()=>{if(O){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(Y.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(Y.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():O.contains(r.target)||B(!1),document.removeEventListener("pointermove",t),Y.current=null};return null!==Y.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[O,B,Y]),f.useEffect(()=>{let e=()=>B(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[B]);let[Z,U]=nu(e=>{let t=I().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=nc(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),$=f.useCallback((e,t,r)=>{let n=!V.current&&!r;(void 0!==E.value&&E.value===t||n)&&(_(e),n&&(V.current=!0))},[E.value]),q=f.useCallback(()=>O?.focus(),[O]),X=f.useCallback((e,t,r)=>{let n=!V.current&&!r;(void 0!==E.value&&E.value===t||n)&&L(e)},[E.value]),G="popper"===n?rY:rB,K=G===rY?{side:l,sideOffset:u,align:c,alignOffset:s,arrowPadding:d,collisionBoundary:p,collisionPadding:h,sticky:v,hideWhenDetached:y,avoidCollisions:x}:{};return(0,b.jsx)(rV,{scope:r,content:O,viewport:k,onViewportChange:D,itemRefCallback:$,selectedItem:M,onItemLeave:q,itemTextRefCallback:X,focusSelectedItem:H,selectedItemText:N,position:n,isPositioned:W,searchRef:Z,children:(0,b.jsx)(ry,{as:w.g7,allowPinchZoom:!0,children:(0,b.jsx)(A,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,m.M)(o,e=>{E.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,b.jsx)(j,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:i,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,b.jsx)(G,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...S,...K,onPlaced:()=>F(!0),ref:R,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:(0,m.M)(S.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||U(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=I().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>z(t)),e.preventDefault()}})})})})})})});rH.displayName="SelectContentImpl";var rB=f.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:n,...o}=e,a=rD(rW,r),i=rz(rW,r),[l,u]=f.useState(null),[c,s]=f.useState(null),d=(0,g.e)(t,e=>s(e)),p=rS(r),h=f.useRef(!1),m=f.useRef(!0),{viewport:y,selectedItem:w,selectedItemText:x,focusSelectedItem:E}=i,O=f.useCallback(()=>{if(a.trigger&&a.valueNode&&l&&c&&y&&w&&x){let e=a.trigger.getBoundingClientRect(),t=c.getBoundingClientRect(),r=a.valueNode.getBoundingClientRect(),o=x.getBoundingClientRect();if("rtl"!==a.dir){let n=o.left-t.left,a=r.left-n,i=e.left-a,u=e.width+i,c=Math.max(u,t.width),s=window.innerWidth-10,d=(0,v.u)(a,[10,s-c]);l.style.minWidth=u+"px",l.style.left=d+"px"}else{let n=t.right-o.right,a=window.innerWidth-r.right-n,i=window.innerWidth-e.right-a,u=e.width+i,c=Math.max(u,t.width),s=window.innerWidth-10,d=(0,v.u)(a,[10,s-c]);l.style.minWidth=u+"px",l.style.right=d+"px"}let i=p(),u=window.innerHeight-20,s=y.scrollHeight,d=window.getComputedStyle(c),f=parseInt(d.borderTopWidth,10),m=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),b=f+m+s+parseInt(d.paddingBottom,10)+g,S=Math.min(5*w.offsetHeight,b),E=window.getComputedStyle(y),O=parseInt(E.paddingTop,10),C=parseInt(E.paddingBottom,10),j=e.top+e.height/2-10,k=w.offsetHeight/2,D=f+m+(w.offsetTop+k);if(D<=j){let e=w===i[i.length-1].ref.current;l.style.bottom="0px";let t=c.clientHeight-y.offsetTop-y.offsetHeight;l.style.height=D+Math.max(u-j,k+(e?C:0)+t+g)+"px"}else{let e=w===i[0].ref.current;l.style.top="0px";let t=Math.max(j,f+y.offsetTop+(e?O:0)+k);l.style.height=t+(b-D)+"px",y.scrollTop=D-j+y.offsetTop}l.style.margin="10px 0",l.style.minHeight=S+"px",l.style.maxHeight=u+"px",n?.(),requestAnimationFrame(()=>h.current=!0)}},[p,a.trigger,a.valueNode,l,c,y,w,x,a.dir,n]);(0,V.b)(()=>O(),[O]);let[C,j]=f.useState();(0,V.b)(()=>{c&&j(window.getComputedStyle(c).zIndex)},[c]);let k=f.useCallback(e=>{e&&!0===m.current&&(O(),E?.(),m.current=!1)},[O,E]);return(0,b.jsx)(rZ,{scope:r,contentWrapper:l,shouldExpandOnScrollRef:h,onScrollButtonChange:k,children:(0,b.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:(0,b.jsx)(S.WV.div,{...o,ref:d,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});rB.displayName="SelectItemAlignedPosition";var rY=f.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...a}=e,i=rj(r);return(0,b.jsx)(tj,{...i,...a,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});rY.displayName="SelectPopperPosition";var[rZ,rU]=rO(rW,{}),r$="SelectViewport",rq=f.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:n,...o}=e,a=rz(r$,r),i=rU(r$,r),l=(0,g.e)(t,a.onViewportChange),u=f.useRef(0);return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,b.jsx)(rx.Slot,{scope:r,children:(0,b.jsx)(S.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:l,style:{position:"relative",flex:1,overflow:"auto",...o.style},onScroll:(0,m.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=i;if(n?.current&&r){let e=Math.abs(u.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let a=o+e,i=Math.min(n,a),l=a-i;r.style.height=i+"px","0px"===r.style.bottom&&(t.scrollTop=l>0?l:0,r.style.justifyContent="flex-end")}}}u.current=t.scrollTop})})})]})});rq.displayName=r$;var rX="SelectGroup",[rG,rK]=rO(rX),rJ=f.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=B();return(0,b.jsx)(rG,{scope:r,id:o,children:(0,b.jsx)(S.WV.div,{role:"group","aria-labelledby":o,...n,ref:t})})});rJ.displayName=rX;var rQ="SelectLabel",r0=f.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=rK(rQ,r);return(0,b.jsx)(S.WV.div,{id:o.id,...n,ref:t})});r0.displayName=rQ;var r1="SelectItem",[r2,r5]=rO(r1),r4=f.forwardRef((e,t)=>{let{__scopeSelect:r,value:n,disabled:o=!1,textValue:a,...i}=e,l=rD(r1,r),u=rz(r1,r),c=l.value===n,[s,d]=f.useState(a??""),[p,h]=f.useState(!1),v=(0,g.e)(t,e=>u.itemRefCallback?.(e,n,o)),y=B(),w=()=>{o||(l.onValueChange(n),l.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,b.jsx)(r2,{scope:r,value:n,disabled:o,textId:y,isSelected:c,onItemTextChange:f.useCallback(e=>{d(t=>t||(e?.textContent??"").trim())},[]),children:(0,b.jsx)(rx.ItemSlot,{scope:r,value:n,disabled:o,textValue:s,children:(0,b.jsx)(S.WV.div,{role:"option","aria-labelledby":y,"data-highlighted":p?"":void 0,"aria-selected":c&&p,"data-state":c?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...i,ref:v,onFocus:(0,m.M)(i.onFocus,()=>h(!0)),onBlur:(0,m.M)(i.onBlur,()=>h(!1)),onPointerUp:(0,m.M)(i.onPointerUp,w),onPointerMove:(0,m.M)(i.onPointerMove,e=>{o?u.onItemLeave?.():e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,m.M)(i.onPointerLeave,e=>{e.currentTarget===document.activeElement&&u.onItemLeave?.()}),onKeyDown:(0,m.M)(i.onKeyDown,e=>{u.searchRef?.current!==""&&" "===e.key||(rw.includes(e.key)&&w()," "===e.key&&e.preventDefault())})})})})});r4.displayName=r1;var r6="SelectItemText",r3=f.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,...a}=e,i=rD(r6,r),l=rz(r6,r),u=r5(r6,r),c=rP(r6,r),[s,d]=f.useState(null),p=(0,g.e)(t,e=>d(e),u.onItemTextChange,e=>l.itemTextRefCallback?.(e,u.value,u.disabled)),v=s?.textContent,m=f.useMemo(()=>(0,b.jsx)("option",{value:u.value,disabled:u.disabled,children:v},u.value),[u.disabled,u.value,v]),{onNativeOptionAdd:y,onNativeOptionRemove:w}=c;return(0,V.b)(()=>(y(m),()=>w(m)),[y,w,m]),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(S.WV.span,{id:u.textId,...a,ref:p}),u.isSelected&&i.valueNode&&!i.valueNodeHasChildren?h.createPortal(a.children,i.valueNode):null]})});r3.displayName=r6;var r8="SelectItemIndicator",r7=f.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return r5(r8,r).isSelected?(0,b.jsx)(S.WV.span,{"aria-hidden":!0,...n,ref:t}):null});r7.displayName=r8;var r9="SelectScrollUpButton",ne=f.forwardRef((e,t)=>{let r=rz(r9,e.__scopeSelect),n=rU(r9,e.__scopeSelect),[o,a]=f.useState(!1),i=(0,g.e)(t,n.onScrollButtonChange);return(0,V.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,b.jsx)(nn,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ne.displayName=r9;var nt="SelectScrollDownButton",nr=f.forwardRef((e,t)=>{let r=rz(nt,e.__scopeSelect),n=rU(nt,e.__scopeSelect),[o,a]=f.useState(!1),i=(0,g.e)(t,n.onScrollButtonChange);return(0,V.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,b.jsx)(nn,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});nr.displayName=nt;var nn=f.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:n,...o}=e,a=rz("SelectScrollButton",r),i=f.useRef(null),l=rS(r),u=f.useCallback(()=>{null!==i.current&&(window.clearInterval(i.current),i.current=null)},[]);return f.useEffect(()=>()=>u(),[u]),(0,V.b)(()=>{let e=l().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[l]),(0,b.jsx)(S.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,m.M)(o.onPointerDown,()=>{null===i.current&&(i.current=window.setInterval(n,50))}),onPointerMove:(0,m.M)(o.onPointerMove,()=>{a.onItemLeave?.(),null===i.current&&(i.current=window.setInterval(n,50))}),onPointerLeave:(0,m.M)(o.onPointerLeave,()=>{u()})})}),no=f.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,b.jsx)(S.WV.div,{"aria-hidden":!0,...n,ref:t})});no.displayName="SelectSeparator";var na="SelectArrow";function ni(e){return""===e||void 0===e}f.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=rj(r),a=rD(na,r),i=rz(na,r);return a.open&&"popper"===i.position?(0,b.jsx)(tT,{...o,...n,ref:t}):null}).displayName=na;var nl=f.forwardRef((e,t)=>{let{value:r,...n}=e,o=f.useRef(null),a=(0,g.e)(t,o),i=function(e){let t=f.useRef({value:e,previous:e});return f.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return f.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[i,r]),(0,b.jsx)(tN,{asChild:!0,children:(0,b.jsx)("select",{...n,ref:a,defaultValue:r})})});function nu(e){let t=(0,E.W)(e),r=f.useRef(""),n=f.useRef(0),o=f.useCallback(e=>{let o=r.current+e;t(o),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),a=f.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return f.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,o,a]}function nc(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(a=a.filter(e=>e!==r));let i=a.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return i!==r?i:void 0}nl.displayName="BubbleSelect";var ns=rR,nd=r_,nf=rN,np=rL,nh=rI,nv=rF,nm=rq,ny=rJ,ng=r0,nw=r4,nb=r3,nx=r7,nS=ne,nE=nr,nO=no},6606:function(e,t,r){"use strict";r.d(t,{W:function(){return o}});var n=r(2265);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},1188:function(e,t,r){"use strict";r.d(t,{b:function(){return o}});var n=r(2265),o=globalThis?.document?n.useLayoutEffect:()=>{}},5095:function(e,t,r){"use strict";r.d(t,{M:function(){return m}});var n=r(2265),o=r(1534);function a(){let e=(0,n.useRef)(!1);return(0,o.L)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}var i=r(8345),l=r(4252),u=r(3576);class c extends n.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function s({children:e,isPresent:t}){let r=(0,n.useId)(),o=(0,n.useRef)(null),a=(0,n.useRef)({width:0,height:0,top:0,left:0});return(0,n.useInsertionEffect)(()=>{let{width:e,height:n,top:i,left:l}=a.current;if(t||!o.current||!e||!n)return;o.current.dataset.motionPopId=r;let u=document.createElement("style");return document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${n}px !important;
            top: ${i}px !important;
            left: ${l}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[t]),n.createElement(c,{isPresent:t,childRef:o,sizeRef:a},n.cloneElement(e,{ref:o}))}let d=({children:e,initial:t,isPresent:r,onExitComplete:o,custom:a,presenceAffectsLayout:i,mode:c})=>{let d=(0,u.h)(f),p=(0,n.useId)(),h=(0,n.useMemo)(()=>({id:p,initial:t,isPresent:r,custom:a,onExitComplete:e=>{for(let t of(d.set(e,!0),d.values()))if(!t)return;o&&o()},register:e=>(d.set(e,!1),()=>d.delete(e))}),i?void 0:[r]);return(0,n.useMemo)(()=>{d.forEach((e,t)=>d.set(t,!1))},[r]),n.useEffect(()=>{r||d.size||!o||o()},[r]),"popLayout"===c&&(e=n.createElement(s,{isPresent:r},e)),n.createElement(l.O.Provider,{value:h},e)};function f(){return new Map}var p=r(8881),h=r(3223);let v=e=>e.key||"",m=({children:e,custom:t,initial:r=!0,onExitComplete:l,exitBeforeEnter:u,presenceAffectsLayout:c=!0,mode:s="sync"})=>{var f;(0,h.k)(!u,"Replace exitBeforeEnter with mode='wait'");let m=(0,n.useContext)(p.p).forceRender||function(){let e=a(),[t,r]=(0,n.useState)(0),o=(0,n.useCallback)(()=>{e.current&&r(t+1)},[t]);return[(0,n.useCallback)(()=>i.Wi.postRender(o),[o]),t]}()[0],y=a(),g=function(e){let t=[];return n.Children.forEach(e,e=>{(0,n.isValidElement)(e)&&t.push(e)}),t}(e),w=g,b=(0,n.useRef)(new Map).current,x=(0,n.useRef)(w),S=(0,n.useRef)(new Map).current,E=(0,n.useRef)(!0);if((0,o.L)(()=>{E.current=!1,function(e,t){e.forEach(e=>{let r=v(e);t.set(r,e)})}(g,S),x.current=w}),f=()=>{E.current=!0,S.clear(),b.clear()},(0,n.useEffect)(()=>()=>f(),[]),E.current)return n.createElement(n.Fragment,null,w.map(e=>n.createElement(d,{key:v(e),isPresent:!0,initial:!!r&&void 0,presenceAffectsLayout:c,mode:s},e)));w=[...w];let O=x.current.map(v),C=g.map(v),j=O.length;for(let e=0;e<j;e++){let t=O[e];-1!==C.indexOf(t)||b.has(t)||b.set(t,void 0)}return"wait"===s&&b.size&&(w=[]),b.forEach((e,r)=>{if(-1!==C.indexOf(r))return;let o=S.get(r);if(!o)return;let a=O.indexOf(r),i=e;i||(i=n.createElement(d,{key:v(o),isPresent:!1,onExitComplete:()=>{b.delete(r);let e=Array.from(S.keys()).filter(e=>!C.includes(e));if(e.forEach(e=>S.delete(e)),x.current=g.filter(t=>{let n=v(t);return n===r||e.includes(n)}),!b.size){if(!1===y.current)return;m(),l&&l()}},custom:t,presenceAffectsLayout:c,mode:s},o),b.set(r,i)),w.splice(a,0,i)}),w=w.map(e=>{let t=e.key;return b.has(t)?e:n.createElement(d,{key:v(e),isPresent:!0,presenceAffectsLayout:c,mode:s},e)}),n.createElement(n.Fragment,null,b.size?w:w.map(e=>(0,n.cloneElement)(e)))}},4724:function(e,t,r){"use strict";r.d(t,{ZP:function(){return tP}});var n=r(7437),o=r(2265),a=r(1994);let i=(e,t,r,n)=>{if("length"===r||"prototype"===r||"arguments"===r||"caller"===r)return;let o=Object.getOwnPropertyDescriptor(e,r),a=Object.getOwnPropertyDescriptor(t,r);(l(o,a)||!n)&&Object.defineProperty(e,r,a)},l=function(e,t){return void 0===e||e.configurable||e.writable===t.writable&&e.enumerable===t.enumerable&&e.configurable===t.configurable&&(e.writable||e.value===t.value)},u=(e,t)=>{let r=Object.getPrototypeOf(t);r!==Object.getPrototypeOf(e)&&Object.setPrototypeOf(e,r)},c=(e,t)=>`/* Wrapped ${e}*/
${t}`,s=Object.getOwnPropertyDescriptor(Function.prototype,"toString"),d=Object.getOwnPropertyDescriptor(Function.prototype.toString,"name"),f=(e,t,r)=>{let n=""===r?"":`with ${r.trim()}() `,o=c.bind(null,n,t.toString());Object.defineProperty(o,"name",d);let{writable:a,enumerable:i,configurable:l}=s;Object.defineProperty(e,"toString",{value:o,writable:a,enumerable:i,configurable:l})},p=new WeakMap,h=new WeakMap;function v(e,{cacheKey:t,cache:r=new Map,maxAge:n}={}){if(0===n)return e;if("number"==typeof n){if(n>2147483647)throw TypeError("The `maxAge` option cannot exceed 2147483647.");if(n<0)throw TypeError("The `maxAge` option should not be a negative number.")}let o=function(...o){let a=t?t(o):o[0],i=r.get(a);if(i)return i.data;let l=e.apply(this,o),u="function"==typeof n?n(...o):n;if(r.set(a,{data:l,maxAge:u?Date.now()+u:Number.POSITIVE_INFINITY}),u&&u>0&&u!==Number.POSITIVE_INFINITY){let t=setTimeout(()=>{r.delete(a)},u);t.unref?.();let n=h.get(e)??new Set;n.add(t),h.set(e,n)}return l};return!function(e,t,{ignoreNonConfigurable:r=!1}={}){let{name:n}=e;for(let n of Reflect.ownKeys(t))i(e,t,n,r);u(e,t),f(e,t,n)}(o,e,{ignoreNonConfigurable:!0}),p.set(o,r),o}function m(e){return"string"==typeof e}function y(e,t,r){return r.indexOf(e)===t}function g(e){return -1===e.indexOf(",")?e:e.split(",")}var w=v(function(e){var t=void 0===e?{}:e,r=t.useFallbackLocale,n=t.fallbackLocale,o=[];if("undefined"!=typeof navigator){for(var a=navigator.languages||[],i=[],l=0;l<a.length;l++){var u=a[l];i=i.concat(g(u))}var c=navigator.language,s=c?g(c):c;o=o.concat(i,s)}return(void 0===r||r)&&o.push(void 0===n?"en-US":n),o.filter(m).map(function e(t){if(!t)return t;if("C"===t||"posix"===t||"POSIX"===t)return"en-US";if(-1!==t.indexOf(".")){var r=t.split(".")[0],n=void 0===r?"":r;return e(n)}if(-1!==t.indexOf("@")){var o=t.split("@")[0],n=void 0===o?"":o;return e(n)}if(-1===t.indexOf("-")||t.toLowerCase()!==t)return t;var a=t.split("-"),i=a[0],l=a[1];return"".concat(i,"-").concat((void 0===l?"":l).toUpperCase())}).filter(y)},{cacheKey:JSON.stringify}),b=v(function(e){return w(e)[0]||null},{cacheKey:JSON.stringify});function x(e,t,r){return function(n,o=r){return t(e(n)+o)}}function S(e){return function(t){return new Date(e(t).getTime()-1)}}function E(e,t){return function(r){return[e(r),t(r)]}}function O(e){if(e instanceof Date)return e.getFullYear();if("number"==typeof e)return e;let t=Number.parseInt(e,10);if("string"==typeof e&&!Number.isNaN(t))return t;throw Error(`Failed to get year from date: ${e}.`)}function C(e){if(e instanceof Date)return e.getMonth();throw Error(`Failed to get month from date: ${e}.`)}function j(e){if(e instanceof Date)return e.getDate();throw Error(`Failed to get year from date: ${e}.`)}function k(e){let t=O(e),r=new Date;return r.setFullYear(t+(-t+1)%100,0,1),r.setHours(0,0,0,0),r}let D=x(O,k,-100),T=x(O,k,100),P=S(T),R=x(O,P,-100);x(O,P,100);let M=E(k,P);function _(e){let t=O(e),r=new Date;return r.setFullYear(t+(-t+1)%10,0,1),r.setHours(0,0,0,0),r}let A=x(O,_,-10),N=x(O,_,10),L=S(N),I=x(O,L,-10);x(O,L,10);let W=E(_,L);function F(e){let t=O(e),r=new Date;return r.setFullYear(t,0,1),r.setHours(0,0,0,0),r}let V=x(O,F,-1),z=x(O,F,1),H=S(z),B=x(O,H,-1);x(O,H,1);let Y=E(F,H);function Z(e,t){return function(r,n=t){let o=O(r),a=C(r)+n,i=new Date;return i.setFullYear(o,a,1),i.setHours(0,0,0,0),e(i)}}function U(e){let t=O(e),r=C(e),n=new Date;return n.setFullYear(t,r,1),n.setHours(0,0,0,0),n}let $=Z(U,-1),q=Z(U,1),X=S(q),G=Z(X,-1);Z(X,1);let K=E(U,X);function J(e,t){return function(r,n=t){let o=O(r),a=C(r),i=j(r)+n,l=new Date;return l.setFullYear(o,a,i),l.setHours(0,0,0,0),e(l)}}function Q(e){let t=O(e),r=C(e),n=j(e),o=new Date;return o.setFullYear(t,r,n),o.setHours(0,0,0,0),o}J(Q,-1);let ee=S(J(Q,1));J(ee,-1),J(ee,1);let et=E(Q,ee);var er={GREGORY:"gregory",HEBREW:"hebrew",ISLAMIC:"islamic",ISO_8601:"iso8601"},en={gregory:["en-CA","en-US","es-AR","es-BO","es-CL","es-CO","es-CR","es-DO","es-EC","es-GT","es-HN","es-MX","es-NI","es-PA","es-PE","es-PR","es-SV","es-VE","pt-BR"],hebrew:["he","he-IL"],islamic:["ar","ar-AE","ar-BH","ar-DZ","ar-EG","ar-IQ","ar-JO","ar-KW","ar-LY","ar-OM","ar-QA","ar-SA","ar-SD","ar-SY","ar-YE","dv","dv-MV","ps","ps-AR"]},eo=new Map;function ea(e){return function(t,r){var n,o,a;return n=new Date(new Date(r).setHours(12)),o=t||b(),eo.has(o)||eo.set(o,new Map),(a=eo.get(o)).has(e)||a.set(e,new Intl.DateTimeFormat(o||void 0,e).format),a.get(e)(n)}}ea({day:"numeric",month:"numeric",year:"numeric"});var ei=ea({day:"numeric"}),el=ea({day:"numeric",month:"long",year:"numeric"}),eu=ea({month:"long"}),ec=ea({month:"long",year:"numeric"}),es=ea({weekday:"short"}),ed=ea({weekday:"long"}),ef=ea({year:"numeric"});function ep(e,t){void 0===t&&(t=er.ISO_8601);var r=e.getDay();switch(t){case er.ISO_8601:return(r+6)%7;case er.ISLAMIC:return(r+1)%7;case er.HEBREW:case er.GREGORY:return r;default:throw Error("Unsupported calendar type.")}}function eh(e,t){return void 0===t&&(t=er.ISO_8601),new Date(O(e),C(e),e.getDate()-ep(e,t))}function ev(e,t){switch(e){case"century":return k(t);case"decade":return _(t);case"year":return F(t);case"month":return U(t);case"day":return Q(t);default:throw Error("Invalid rangeType: ".concat(e))}}function em(e,t){switch(e){case"century":return T(t);case"decade":return N(t);case"year":return z(t);case"month":return q(t);default:throw Error("Invalid rangeType: ".concat(e))}}function ey(e,t){switch(e){case"century":return P(t);case"decade":return L(t);case"year":return H(t);case"month":return X(t);case"day":return ee(t);default:throw Error("Invalid rangeType: ".concat(e))}}function eg(e,t){switch(e){case"century":return M(t);case"decade":return W(t);case"year":return Y(t);case"month":return K(t);case"day":return et(t);default:throw Error("Invalid rangeType: ".concat(e))}}function ew(e,t,r){return r.map(function(r){return(t||ef)(e,r)}).join(" – ")}function eb(e,t){void 0===t&&(t=er.ISO_8601);var r=e.getDay();switch(t){case er.ISLAMIC:case er.HEBREW:return 5===r||6===r;case er.ISO_8601:case er.GREGORY:return 6===r||0===r;default:throw Error("Unsupported calendar type.")}}var ex="react-calendar__navigation";function eS(e){var t,r=e.activeStartDate,o=e.drillUp,a=e.formatMonthYear,i=void 0===a?ec:a,l=e.formatYear,u=void 0===l?ef:l,c=e.locale,s=e.maxDate,d=e.minDate,f=e.navigationAriaLabel,p=e.navigationAriaLive,h=e.navigationLabel,v=e.next2AriaLabel,m=e.next2Label,y=void 0===m?"\xbb":m,g=e.nextAriaLabel,w=e.nextLabel,x=void 0===w?"›":w,S=e.prev2AriaLabel,E=e.prev2Label,O=void 0===E?"\xab":E,C=e.prevAriaLabel,j=e.prevLabel,k=void 0===j?"‹":j,T=e.setActiveStartDate,P=e.showDoubleView,_=e.view,L=e.views.indexOf(_)>0,F="century"!==_,H=function(e,t){switch(e){case"century":return D(t);case"decade":return A(t);case"year":return V(t);case"month":return $(t);default:throw Error("Invalid rangeType: ".concat(e))}}(_,r),Y=F?function(e,t){switch(e){case"decade":return A(t,-100);case"year":return V(t,-10);case"month":return $(t,-12);default:throw Error("Invalid rangeType: ".concat(e))}}(_,r):void 0,Z=em(_,r),U=F?function(e,t){switch(e){case"decade":return N(t,100);case"year":return z(t,10);case"month":return q(t,12);default:throw Error("Invalid rangeType: ".concat(e))}}(_,r):void 0,X=function(){if(0>H.getFullYear())return!0;var e=function(e,t){switch(e){case"century":return R(t);case"decade":return I(t);case"year":return B(t);case"month":return G(t);default:throw Error("Invalid rangeType: ".concat(e))}}(_,r);return d&&d>=e}(),K=F&&function(){if(0>Y.getFullYear())return!0;var e=function(e,t){switch(e){case"decade":return I(t,-100);case"year":return B(t,-10);case"month":return G(t,-12);default:throw Error("Invalid rangeType: ".concat(e))}}(_,r);return d&&d>=e}(),J=s&&s<Z,Q=F&&s&&s<U;function ee(e){var t=function(){switch(_){case"century":return ew(c,u,M(e));case"decade":return ew(c,u,W(e));case"year":return u(c,e);case"month":return i(c,e);default:throw Error("Invalid view: ".concat(_,"."))}}();return h?h({date:e,label:t,locale:c||b()||void 0,view:_}):t}return(0,n.jsxs)("div",{className:ex,children:[null!==O&&F?(0,n.jsx)("button",{"aria-label":void 0===S?"":S,className:"".concat(ex,"__arrow ").concat(ex,"__prev2-button"),disabled:K,onClick:function(){T(Y,"prev2")},type:"button",children:O}):null,null!==k&&(0,n.jsx)("button",{"aria-label":void 0===C?"":C,className:"".concat(ex,"__arrow ").concat(ex,"__prev-button"),disabled:X,onClick:function(){T(H,"prev")},type:"button",children:k}),(t="".concat(ex,"__label"),(0,n.jsxs)("button",{"aria-label":void 0===f?"":f,"aria-live":p,className:t,disabled:!L,onClick:o,style:{flexGrow:1},type:"button",children:[(0,n.jsx)("span",{className:"".concat(t,"__labelText ").concat(t,"__labelText--from"),children:ee(r)}),P?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("span",{className:"".concat(t,"__divider"),children:" – "}),(0,n.jsx)("span",{className:"".concat(t,"__labelText ").concat(t,"__labelText--to"),children:ee(Z)})]}):null]})),null!==x&&(0,n.jsx)("button",{"aria-label":void 0===g?"":g,className:"".concat(ex,"__arrow ").concat(ex,"__next-button"),disabled:J,onClick:function(){T(Z,"next")},type:"button",children:x}),null!==y&&F?(0,n.jsx)("button",{"aria-label":void 0===v?"":v,className:"".concat(ex,"__arrow ").concat(ex,"__next2-button"),disabled:Q,onClick:function(){T(U,"next2")},type:"button",children:y}):null]})}var eE=function(){return(eE=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},eO=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function eC(e){return"".concat(e,"%")}function ej(e){var t=e.children,r=e.className,a=e.count,i=e.direction,l=e.offset,u=e.style,c=e.wrap,s=eO(e,["children","className","count","direction","offset","style","wrap"]);return(0,n.jsx)("div",eE({className:r,style:eE({display:"flex",flexDirection:i,flexWrap:c?"wrap":"nowrap"},u)},s,{children:o.Children.map(t,function(e,t){var r=l&&0===t?eC(100*l/a):null;return(0,o.cloneElement)(e,eE(eE({},e.props),{style:{flexBasis:eC(100/a),flexShrink:0,flexGrow:0,overflow:"hidden",marginLeft:r,marginInlineStart:r,marginInlineEnd:0}}))})}))}function ek(e,t){return t[0]<=e&&t[1]>=e}function eD(e,t){return ek(e[0],t)||ek(e[1],t)}function eT(e,t,r){var n=eD(t,e),o=[];if(n){o.push(r);var a=ek(e[0],t),i=ek(e[1],t);a&&o.push("".concat(r,"Start")),i&&o.push("".concat(r,"End")),a&&i&&o.push("".concat(r,"BothEnds"))}return o}function eP(e){for(var t=e.className,r=e.count,o=e.dateTransform,a=e.dateType,i=e.end,l=e.hover,u=e.offset,c=e.renderTile,s=e.start,d=e.step,f=void 0===d?1:d,p=e.value,h=e.valueType,v=[],m=s;m<=i;m+=f){var y=o(m);v.push(c({classes:function(e){if(!e)throw Error("args is required");var t=e.value,r=e.date,n=e.hover,o="react-calendar__tile",a=[o];if(!r)return a;var i=new Date,l=function(){if(Array.isArray(r))return r;var t=e.dateType;if(!t)throw Error("dateType is required when date is not an array of two dates");return eg(t,r)}();if(ek(i,l)&&a.push("".concat(o,"--now")),!t||!(Array.isArray(t)?null!==t[0]&&null!==t[1]:null!==t))return a;var u=function(){if(Array.isArray(t))return t;var r=e.valueType;if(!r)throw Error("valueType is required when value is not an array of two dates");return eg(r,t)}();u[0]<=l[0]&&u[1]>=l[1]?a.push("".concat(o,"--active")):eD(u,l)&&a.push("".concat(o,"--hasActive"));var c=eT(u,l,"".concat(o,"--range"));a.push.apply(a,c);var s=Array.isArray(t)?t:[t];if(n&&1===s.length){var d=eT(n>u[0]?[u[0],n]:[n,u[0]],l,"".concat(o,"--hover"));a.push.apply(a,d)}return a}({date:y,dateType:a,hover:l,value:p,valueType:h}),date:y}))}return(0,n.jsx)(ej,{className:t,count:void 0===r?3:r,offset:u,wrap:!0,children:v})}function eR(e){var t=e.activeStartDate,r=e.children,i=e.classes,l=e.date,u=e.formatAbbr,c=e.locale,s=e.maxDate,d=e.maxDateTransform,f=e.minDate,p=e.minDateTransform,h=e.onClick,v=e.onMouseOver,m=e.style,y=e.tileClassName,g=e.tileContent,w=e.tileDisabled,b=e.view,x=(0,o.useMemo)(function(){return"function"==typeof y?y({activeStartDate:t,date:l,view:b}):y},[t,l,y,b]),S=(0,o.useMemo)(function(){return"function"==typeof g?g({activeStartDate:t,date:l,view:b}):g},[t,l,g,b]);return(0,n.jsxs)("button",{className:(0,a.Z)(i,x),disabled:f&&p(f)>l||s&&d(s)<l||(null==w?void 0:w({activeStartDate:t,date:l,view:b})),onClick:h?function(e){return h(l,e)}:void 0,onFocus:v?function(){return v(l)}:void 0,onMouseOver:v?function(){return v(l)}:void 0,style:m,type:"button",children:[u?(0,n.jsx)("abbr",{"aria-label":u(c,l),children:r}):r,S]})}var eM=function(){return(eM=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},e_=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r},eA="react-calendar__century-view__decades__decade";function eN(e){var t=e.classes,r=void 0===t?[]:t,o=e.currentCentury,a=e.formatYear,i=e_(e,["classes","currentCentury","formatYear"]),l=i.date,u=i.locale,c=[];return r&&c.push.apply(c,r),eA&&c.push(eA),k(l).getFullYear()!==o&&c.push("".concat(eA,"--neighboringCentury")),(0,n.jsx)(eR,eM({},i,{classes:c,maxDateTransform:L,minDateTransform:_,view:"century",children:ew(u,void 0===a?ef:a,W(l))}))}var eL=function(){return(eL=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},eI=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function eW(e){var t=e.activeStartDate,r=e.hover,o=e.showNeighboringCentury,a=e.value,i=e.valueType,l=eI(e,["activeStartDate","hover","showNeighboringCentury","value","valueType"]),u=O(k(t)),c=u+(o?119:99);return(0,n.jsx)(eP,{className:"react-calendar__century-view__decades",dateTransform:_,dateType:"decade",end:c,hover:r,renderTile:function(e){var r=e.date,o=eI(e,["date"]);return(0,n.jsx)(eN,eL({},l,o,{activeStartDate:t,currentCentury:u,date:r}),r.getTime())},start:u,step:10,value:a,valueType:i})}var eF=function(){return(eF=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function eV(e){return(0,n.jsx)("div",{className:"react-calendar__century-view",children:(0,n.jsx)(eW,eF({},e))})}var ez=function(){return(ez=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},eH=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r},eB="react-calendar__decade-view__years__year";function eY(e){var t=e.classes,r=void 0===t?[]:t,o=e.currentDecade,a=e.formatYear,i=void 0===a?ef:a,l=eH(e,["classes","currentDecade","formatYear"]),u=l.date,c=l.locale,s=[];return r&&s.push.apply(s,r),eB&&s.push(eB),_(u).getFullYear()!==o&&s.push("".concat(eB,"--neighboringDecade")),(0,n.jsx)(eR,ez({},l,{classes:s,maxDateTransform:H,minDateTransform:F,view:"decade",children:i(c,u)}))}var eZ=function(){return(eZ=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},eU=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function e$(e){var t=e.activeStartDate,r=e.hover,o=e.showNeighboringDecade,a=e.value,i=e.valueType,l=eU(e,["activeStartDate","hover","showNeighboringDecade","value","valueType"]),u=O(_(t)),c=u+(o?11:9);return(0,n.jsx)(eP,{className:"react-calendar__decade-view__years",dateTransform:F,dateType:"year",end:c,hover:r,renderTile:function(e){var r=e.date,o=eU(e,["date"]);return(0,n.jsx)(eY,eZ({},l,o,{activeStartDate:t,currentDecade:u,date:r}),r.getTime())},start:u,value:a,valueType:i})}var eq=function(){return(eq=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function eX(e){return(0,n.jsx)("div",{className:"react-calendar__decade-view",children:(0,n.jsx)(e$,eq({},e))})}var eG=function(){return(eG=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},eK=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r},eJ=function(e,t,r){if(r||2==arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};function eQ(e){var t=e.classes,r=e.formatMonth,o=e.formatMonthYear,a=eK(e,["classes","formatMonth","formatMonthYear"]),i=a.date,l=a.locale;return(0,n.jsx)(eR,eG({},a,{classes:eJ(eJ([],void 0===t?[]:t,!0),["react-calendar__year-view__months__month"],!1),formatAbbr:void 0===o?ec:o,maxDateTransform:X,minDateTransform:U,view:"year",children:(void 0===r?eu:r)(l,i)}))}var e0=function(){return(e0=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},e1=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function e2(e){var t=e.activeStartDate,r=e.hover,o=e.value,a=e.valueType,i=e1(e,["activeStartDate","hover","value","valueType"]),l=O(t);return(0,n.jsx)(eP,{className:"react-calendar__year-view__months",dateTransform:function(e){var t=new Date;return t.setFullYear(l,e,1),U(t)},dateType:"month",end:11,hover:r,renderTile:function(e){var r=e.date,o=e1(e,["date"]);return(0,n.jsx)(eQ,e0({},i,o,{activeStartDate:t,date:r}),r.getTime())},start:0,value:o,valueType:a})}var e5=function(){return(e5=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function e4(e){return(0,n.jsx)("div",{className:"react-calendar__year-view",children:(0,n.jsx)(e2,e5({},e))})}var e6=function(){return(e6=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},e3=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r},e8="react-calendar__month-view__days__day";function e7(e){var t=e.calendarType,r=e.classes,o=void 0===r?[]:r,a=e.currentMonthIndex,i=e.formatDay,l=e.formatLongDate,u=e3(e,["calendarType","classes","currentMonthIndex","formatDay","formatLongDate"]),c=u.date,s=u.locale,d=[];return o&&d.push.apply(d,o),e8&&d.push(e8),eb(c,t)&&d.push("".concat(e8,"--weekend")),c.getMonth()!==a&&d.push("".concat(e8,"--neighboringMonth")),(0,n.jsx)(eR,e6({},u,{classes:d,formatAbbr:void 0===l?el:l,maxDateTransform:ee,minDateTransform:Q,view:"month",children:(void 0===i?ei:i)(s,c)}))}var e9=function(){return(e9=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},te=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function tt(e){var t=e.activeStartDate,r=e.calendarType,o=e.hover,a=e.showFixedNumberOfWeeks,i=e.showNeighboringMonth,l=e.value,u=e.valueType,c=te(e,["activeStartDate","calendarType","hover","showFixedNumberOfWeeks","showNeighboringMonth","value","valueType"]),s=O(t),d=C(t),f=a||i,p=ep(t,r),h=(f?-p:0)+1,v=function(){if(a)return h+42-1;var e=j(X(t));if(i){var n=new Date;return n.setFullYear(s,d,e),n.setHours(0,0,0,0),e+(7-ep(n,r)-1)}return e}();return(0,n.jsx)(eP,{className:"react-calendar__month-view__days",count:7,dateTransform:function(e){var t=new Date;return t.setFullYear(s,d,e),Q(t)},dateType:"day",hover:o,end:v,renderTile:function(e){var o=e.date,a=te(e,["date"]);return(0,n.jsx)(e7,e9({},c,a,{activeStartDate:t,calendarType:r,currentMonthIndex:d,date:o}),o.getTime())},offset:f?0:p,start:h,value:l,valueType:u})}var tr="react-calendar__month-view__weekdays",tn="".concat(tr,"__weekday");function to(e){for(var t=e.calendarType,r=e.formatShortWeekday,o=void 0===r?es:r,i=e.formatWeekday,l=void 0===i?ed:i,u=e.locale,c=e.onMouseLeave,s=U(new Date),d=O(s),f=C(s),p=[],h=1;h<=7;h+=1){var v=new Date(d,f,h-ep(s,t)),m=l(u,v);p.push((0,n.jsx)("div",{className:(0,a.Z)(tn,v.getDay()===new Date().getDay()&&"".concat(tn,"--current"),eb(v,t)&&"".concat(tn,"--weekend")),children:(0,n.jsx)("abbr",{"aria-label":m,title:m,children:o(u,v).replace(".","")})},h))}return(0,n.jsx)(ej,{className:tr,count:7,onFocus:c,onMouseOver:c,children:p})}var ta=function(){return(ta=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},ti=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r},tl="react-calendar__tile";function tu(e){var t=e.onClickWeekNumber,r=e.weekNumber,o=(0,n.jsx)("span",{children:r});if(t){var a=e.date,i=e.onClickWeekNumber,l=e.weekNumber,u=ti(e,["date","onClickWeekNumber","weekNumber"]);return(0,n.jsx)("button",ta({},u,{className:tl,onClick:function(e){return i(l,a,e)},type:"button",children:o}))}e.date,e.onClickWeekNumber,e.weekNumber;var u=ti(e,["date","onClickWeekNumber","weekNumber"]);return(0,n.jsx)("div",ta({},u,{className:tl,children:o}))}function tc(e){var t=e.activeStartDate,r=e.calendarType,o=e.onClickWeekNumber,a=e.onMouseLeave,i=e.showFixedNumberOfWeeks?6:1+Math.ceil((j(X(t))-(7-ep(t,r)))/7),l=function(){for(var e=O(t),n=C(t),o=j(t),a=[],l=0;l<i;l+=1)a.push(eh(new Date(e,n,o+7*l),r));return a}(),u=l.map(function(e){return function(e,t){void 0===t&&(t=er.ISO_8601);var r,n=t===er.GREGORY?er.GREGORY:er.ISO_8601,o=eh(e,t),a=O(e)+1;do r=eh(new Date(a,0,n===er.ISO_8601?4:1),t),a-=1;while(e<r);return Math.round((o.getTime()-r.getTime())/(864e5*7))+1}(e,r)});return(0,n.jsx)(ej,{className:"react-calendar__month-view__weekNumbers",count:i,direction:"column",onFocus:a,onMouseOver:a,style:{flexBasis:"calc(100% * (1 / 8)",flexShrink:0},children:u.map(function(e,t){var r=l[t];if(!r)throw Error("date is not defined");return(0,n.jsx)(tu,{date:r,onClickWeekNumber:o,weekNumber:e},e)})})}var ts=function(){return(ts=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},td=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function tf(e){var t=e.activeStartDate,r=e.locale,o=e.onMouseLeave,i=e.showFixedNumberOfWeeks,l=e.calendarType,u=void 0===l?function(e){if(e)for(var t=0,r=Object.entries(en);t<r.length;t++){var n=r[t],o=n[0];if(n[1].includes(e))return o}return er.ISO_8601}(r):l,c=e.formatShortWeekday,s=e.formatWeekday,d=e.onClickWeekNumber,f=e.showWeekNumbers,p=td(e,["calendarType","formatShortWeekday","formatWeekday","onClickWeekNumber","showWeekNumbers"]),h="react-calendar__month-view";return(0,n.jsx)("div",{className:(0,a.Z)(h,f?"".concat(h,"--weekNumbers"):""),children:(0,n.jsxs)("div",{style:{display:"flex",alignItems:"flex-end"},children:[f?(0,n.jsx)(tc,{activeStartDate:t,calendarType:u,onClickWeekNumber:d,onMouseLeave:o,showFixedNumberOfWeeks:i}):null,(0,n.jsxs)("div",{style:{flexGrow:1,width:"100%"},children:[(0,n.jsx)(to,{calendarType:u,formatShortWeekday:c,formatWeekday:s,locale:r,onMouseLeave:o}),(0,n.jsx)(tt,ts({calendarType:u},p))]})]})})}var tp=function(){return(tp=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},th="react-calendar",tv=["century","decade","year","month"],tm=["decade","year","month","day"],ty=new Date;ty.setFullYear(1,0,1),ty.setHours(0,0,0,0);var tg=new Date(864e13);function tw(e){return e instanceof Date?e:new Date(e)}function tb(e,t){return tv.slice(tv.indexOf(e),tv.indexOf(t)+1)}function tx(e,t,r){return e&&-1!==tb(t,r).indexOf(e)?e:r}function tS(e){return tm[tv.indexOf(e)]}function tE(e,t){var r,n=e.value,o=e.minDate,a=e.maxDate,i=e.maxDetail,l=function(e,t){var r=Array.isArray(e)?e[t]:e;if(!r)return null;var n=tw(r);if(Number.isNaN(n.getTime()))throw Error("Invalid date: ".concat(e));return n}(n,t);if(!l)return null;var u=tS(i);return r=function(){switch(t){case 0:return ev(u,l);case 1:return ey(u,l);default:throw Error("Invalid index value: ".concat(t))}}(),o&&o>r?o:a&&a<r?a:r}var tO=function(e){return tE(e,0)},tC=function(e){return tE(e,1)},tj=function(e){return[tO,tC].map(function(t){return t(e)})};function tk(e){var t=e.maxDate,r=e.maxDetail,n=e.minDate,o=e.minDetail,a=e.value;return ev(tx(e.view,o,r),tO({value:a,minDate:n,maxDate:t,maxDetail:r})||new Date)}function tD(e){return e&&(!Array.isArray(e)||1===e.length)}function tT(e,t){return e instanceof Date&&t instanceof Date&&e.getTime()===t.getTime()}var tP=(0,o.forwardRef)(function(e,t){var r,i,l,u,c,s,d,f,p,h,v,m,y,g,w=e.activeStartDate,b=e.allowPartialRange,x=e.calendarType,S=e.className,E=e.defaultActiveStartDate,O=e.defaultValue,C=e.defaultView,j=e.formatDay,k=e.formatLongDate,D=e.formatMonth,T=e.formatMonthYear,P=e.formatShortWeekday,R=e.formatWeekday,M=e.formatYear,_=e.goToRangeStartOnSelect,A=void 0===_||_,N=e.inputRef,L=e.locale,I=e.maxDate,W=void 0===I?tg:I,F=e.maxDetail,V=void 0===F?"month":F,z=e.minDate,H=void 0===z?ty:z,B=e.minDetail,Y=void 0===B?"century":B,Z=e.navigationAriaLabel,U=e.navigationAriaLive,$=e.navigationLabel,q=e.next2AriaLabel,X=e.next2Label,G=e.nextAriaLabel,K=e.nextLabel,J=e.onActiveStartDateChange,Q=e.onChange,ee=e.onClickDay,et=e.onClickDecade,er=e.onClickMonth,en=e.onClickWeekNumber,eo=e.onClickYear,ea=e.onDrillDown,ei=e.onDrillUp,el=e.onViewChange,eu=e.prev2AriaLabel,ec=e.prev2Label,es=e.prevAriaLabel,ed=e.prevLabel,ef=e.returnValue,ep=void 0===ef?"start":ef,eh=e.selectRange,eg=e.showDoubleView,ew=e.showFixedNumberOfWeeks,eb=e.showNavigation,ex=e.showNeighboringCentury,eE=e.showNeighboringDecade,eO=e.showNeighboringMonth,eC=void 0===eO||eO,ej=e.showWeekNumbers,ek=e.tileClassName,eD=e.tileContent,eT=e.tileDisabled,eP=e.value,eR=e.view,eM=(0,o.useState)(E),e_=eM[0],eA=eM[1],eN=(0,o.useState)(null),eL=eN[0],eI=eN[1],eW=(0,o.useState)(Array.isArray(O)?O.map(function(e){return null!==e?tw(e):null}):null!=O?tw(O):null),eF=eW[0],ez=eW[1],eH=(0,o.useState)(C),eB=eH[0],eY=eH[1],eZ=w||e_||(i=(r={activeStartDate:w,defaultActiveStartDate:E,defaultValue:O,defaultView:C,maxDate:W,maxDetail:V,minDate:H,minDetail:Y,value:eP,view:eR}).activeStartDate,l=r.defaultActiveStartDate,u=r.defaultValue,c=r.defaultView,s=r.maxDate,d=r.maxDetail,f=r.minDate,p=r.minDetail,h=r.value,m=tx(v=r.view,p,d),(y=i||l)?ev(m,y):tk({maxDate:s,maxDetail:d,minDate:f,minDetail:p,value:h||u,view:v||c})),eU=(g=eh&&tD(eF)?eF:void 0!==eP?eP:eF)?Array.isArray(g)?g.map(function(e){return null!==e?tw(e):null}):null!==g?tw(g):null:null,e$=tS(V),eq=tx(eR||eB,Y,V),eG=tb(Y,V),eK=eh?eL:null,eJ=eG.indexOf(eq)<eG.length-1,eQ=eG.indexOf(eq)>0,e0=(0,o.useCallback)(function(e){return(function(){switch(ep){case"start":return tO;case"end":return tC;case"range":return tj;default:throw Error("Invalid returnValue.")}})()({maxDate:W,maxDetail:V,minDate:H,value:e})},[W,V,H,ep]),e1=(0,o.useCallback)(function(e,t){eA(e),J&&!tT(eZ,e)&&J({action:t,activeStartDate:e,value:eU,view:eq})},[eZ,J,eU,eq]),e2=(0,o.useCallback)(function(e,t){var r=function(){switch(eq){case"century":return et;case"decade":return eo;case"year":return er;case"month":return ee;default:throw Error("Invalid view: ".concat(eq,"."))}}();r&&r(e,t)},[ee,et,er,eo,eq]),e5=(0,o.useCallback)(function(e,t){if(eJ){e2(e,t);var r=eG[eG.indexOf(eq)+1];if(!r)throw Error("Attempted to drill down from the lowest view.");eA(e),eY(r);var n={action:"drillDown",activeStartDate:e,value:eU,view:r};J&&!tT(eZ,e)&&J(n),el&&eq!==r&&el(n),ea&&ea(n)}},[eZ,eJ,J,e2,ea,el,eU,eq,eG]),e6=(0,o.useCallback)(function(){if(eQ){var e=eG[eG.indexOf(eq)-1];if(!e)throw Error("Attempted to drill up from the highest view.");var t=ev(e,eZ);eA(t),eY(e);var r={action:"drillUp",activeStartDate:t,value:eU,view:e};J&&!tT(eZ,t)&&J(r),el&&eq!==e&&el(r),ei&&ei(r)}},[eZ,eQ,J,ei,el,eU,eq,eG]),e3=(0,o.useCallback)(function(e,t){e2(e,t);var r,n,o=eh&&!tD(eU);if(eh){if(o)r=ev(e$,e);else{if(!eU)throw Error("previousValue is required");if(Array.isArray(eU))throw Error("previousValue must not be an array");r=[ev(e$,(n=[eU,e].sort(function(e,t){return e.getTime()-t.getTime()}))[0]),ey(e$,n[1])]}}else r=e0(e);var a=!eh||o||A?tk({maxDate:W,maxDetail:V,minDate:H,minDetail:Y,value:r,view:eq}):null;t.persist(),eA(a),ez(r);var i={action:"onChange",activeStartDate:a,value:r,view:eq};if(J&&!tT(eZ,a)&&J(i),Q){if(eh){if(tD(r)){if(b){if(Array.isArray(r))throw Error("value must not be an array");Q([r||null,null],t)}}else Q(r||null,t)}else Q(r||null,t)}},[eZ,b,e0,A,W,V,H,Y,J,Q,e2,eh,eU,e$,eq]);function e8(e){eI(e)}function e7(){eI(null)}function e9(e){var t={activeStartDate:e?em(eq,eZ):ev(eq,eZ),hover:eK,locale:L,maxDate:W,minDate:H,onClick:eJ?e5:e3,onMouseOver:eh?e8:void 0,tileClassName:ek,tileContent:eD,tileDisabled:eT,value:eU,valueType:e$};switch(eq){case"century":return(0,n.jsx)(eV,tp({formatYear:M,showNeighboringCentury:ex},t));case"decade":return(0,n.jsx)(eX,tp({formatYear:M,showNeighboringDecade:eE},t));case"year":return(0,n.jsx)(e4,tp({formatMonth:D,formatMonthYear:T},t));case"month":return(0,n.jsx)(tf,tp({calendarType:x,formatDay:j,formatLongDate:k,formatShortWeekday:P,formatWeekday:R,onClickWeekNumber:en,onMouseLeave:eh?e7:void 0,showFixedNumberOfWeeks:void 0!==ew?ew:eg,showNeighboringMonth:eC,showWeekNumbers:ej},t));default:throw Error("Invalid view: ".concat(eq,"."))}}(0,o.useImperativeHandle)(t,function(){return{activeStartDate:eZ,drillDown:e5,drillUp:e6,onChange:e3,setActiveStartDate:e1,value:eU,view:eq}},[eZ,e5,e6,e3,e1,eU,eq]);var te=Array.isArray(eU)?eU:[eU];return(0,n.jsxs)("div",{className:(0,a.Z)(th,eh&&1===te.length&&"".concat(th,"--selectRange"),eg&&"".concat(th,"--doubleView"),S),ref:N,children:[void 0===eb||eb?(0,n.jsx)(eS,{activeStartDate:eZ,drillUp:e6,formatMonthYear:T,formatYear:M,locale:L,maxDate:W,minDate:H,navigationAriaLabel:Z,navigationAriaLive:U,navigationLabel:$,next2AriaLabel:q,next2Label:X,nextAriaLabel:G,nextLabel:K,prev2AriaLabel:eu,prev2Label:ec,prevAriaLabel:es,prevLabel:ed,setActiveStartDate:e1,showDoubleView:eg,view:eq,views:eG}):null,(0,n.jsxs)("div",{className:"".concat(th,"__viewContainer"),onBlur:eh?e7:void 0,onMouseLeave:eh?e7:void 0,children:[e9(),eg?e9(!0):null]})]})})},8819:function(e,t,r){"use strict";r.d(t,{YD:function(){return c}});var n=r(2265),o=Object.defineProperty,a=new Map,i=new WeakMap,l=0,u=void 0;function c({threshold:e,delay:t,trackVisibility:r,rootMargin:o,root:c,triggerOnce:s,skip:d,initialInView:f,fallbackInView:p,onChange:h}={}){var v;let[m,y]=n.useState(null),g=n.useRef(h),[w,b]=n.useState({inView:!!f,entry:void 0});g.current=h,n.useEffect(()=>{let n;if(!d&&m)return n=function(e,t,r={},n=u){if(void 0===window.IntersectionObserver&&void 0!==n){let o=e.getBoundingClientRect();return t(n,{isIntersecting:n,target:e,intersectionRatio:"number"==typeof r.threshold?r.threshold:0,time:0,boundingClientRect:o,intersectionRect:o,rootBounds:o}),()=>{}}let{id:o,observer:c,elements:s}=function(e){let t=Object.keys(e).sort().filter(t=>void 0!==e[t]).map(t=>{var r;return`${t}_${"root"===t?(r=e.root)?(i.has(r)||(l+=1,i.set(r,l.toString())),i.get(r)):"0":e[t]}`}).toString(),r=a.get(t);if(!r){let n;let o=new Map,i=new IntersectionObserver(t=>{t.forEach(t=>{var r;let a=t.isIntersecting&&n.some(e=>t.intersectionRatio>=e);e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=a),null==(r=o.get(t.target))||r.forEach(e=>{e(a,t)})})},e);n=i.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),r={id:t,observer:i,elements:o},a.set(t,r)}return r}(r),d=s.get(e)||[];return s.has(e)||s.set(e,d),d.push(t),c.observe(e),function(){d.splice(d.indexOf(t),1),0===d.length&&(s.delete(e),c.unobserve(e)),0===s.size&&(c.disconnect(),a.delete(o))}}(m,(e,t)=>{b({inView:e,entry:t}),g.current&&g.current(e,t),t.isIntersecting&&s&&n&&(n(),n=void 0)},{root:c,rootMargin:o,threshold:e,trackVisibility:r,delay:t},p),()=>{n&&n()}},[Array.isArray(e)?e.toString():e,m,c,o,s,d,r,p,t]);let x=null==(v=w.entry)?void 0:v.target,S=n.useRef(void 0);m||!x||s||d||S.current===x||(S.current=x,b({inView:!!f,entry:void 0}));let E=[y,w.inView,w.entry];return E.ref=E[0],E.inView=E[1],E.entry=E[2],E}n.Component}}]);