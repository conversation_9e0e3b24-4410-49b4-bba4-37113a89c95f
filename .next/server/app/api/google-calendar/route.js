"use strict";(()=>{var e={};e.id=826,e.ids=[826],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4679:(e,a,t)=>{t.r(a),t.d(a,{originalPathname:()=>g,patchFetch:()=>v,requestAsyncStorage:()=>p,routeModule:()=>u,serverHooks:()=>m,staticGenerationAsyncStorage:()=>d});var r={};t.r(r),t.d(r,{GET:()=>c,POST:()=>s});var o=t(9303),n=t(8716),i=t(670),l=t(7070);async function s(e){try{let{name:a,email:t,phone:r,date:o,time:n,service:i}=await e.json();console.log("Creating Google Calendar event:",{name:a,email:t,phone:r,date:o,time:n,service:i,timestamp:new Date().toISOString()});let s={summary:`Financial Consultation - ${a}`,description:`
        Client: ${a}
        Email: ${t}
        Phone: ${r}
        Service: ${i}
        
        This is a financial planning consultation with Emanuel Ibarra.
      `,start:{dateTime:`${o}T${n}:00-05:00`,timeZone:"America/Chicago"},end:{dateTime:`${o}T${String(parseInt(n.split(":")[0])+1).padStart(2,"0")}:${n.split(":")[1]}:00-05:00`,timeZone:"America/Chicago"},attendees:[{email:"<EMAIL>"},{email:t}]};return l.NextResponse.json({success:!0,message:"Calendar event created successfully",eventId:"simulated-"+Date.now(),eventData:s})}catch(e){return console.error("Google Calendar API error:",e),l.NextResponse.json({error:"Failed to create calendar event"},{status:500})}}async function c(e){try{let e=new Date,a=[];for(let t=1;t<=14;t++){let r=new Date(e);if(r.setDate(e.getDate()+t),0===r.getDay()||6===r.getDay())continue;let o=r.toISOString().split("T")[0];a.push({date:o,time:"09:00",available:!0},{date:o,time:"10:00",available:!0},{date:o,time:"11:00",available:!0}),a.push({date:o,time:"13:00",available:!0},{date:o,time:"14:00",available:!0},{date:o,time:"15:00",available:!0},{date:o,time:"16:00",available:!0})}return l.NextResponse.json({success:!0,availableSlots:a})}catch(e){return console.error("Error fetching available slots:",e),l.NextResponse.json({error:"Failed to fetch available slots"},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/google-calendar/route",pathname:"/api/google-calendar",filename:"route",bundlePath:"app/api/google-calendar/route"},resolvedPagePath:"/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/elgAto/emanuel/emanuel-ibarra-final-theme/app/app/api/google-calendar/route.ts",nextConfigOutput:"export",userland:r}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:m}=u,g="/api/google-calendar/route";function v(){return(0,i.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:d})}}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[276,972],()=>t(4679));module.exports=r})();