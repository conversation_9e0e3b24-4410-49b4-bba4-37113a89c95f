"use strict";(()=>{var e={};e.id=386,e.ids=[386],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6955:(e,t,o)=>{o.r(t),o.d(t,{originalPathname:()=>p,patchFetch:()=>g,requestAsyncStorage:()=>u,routeModule:()=>l,serverHooks:()=>m,staticGenerationAsyncStorage:()=>d});var a={};o.r(a),o.d(a,{POST:()=>c});var r=o(9303),n=o(8716),i=o(670),s=o(7070);async function c(e){try{let{name:t,email:o,phone:a,subject:r,message:n,preferredContact:i}=await e.json();if(!t||!o||!r||!n)return s.NextResponse.json({error:"Missing required fields"},{status:400});let c={id:Date.now().toString(),name:t,email:o,phone:a||"",subject:r,message:n,preferredContact:i||"email",status:"new",createdAt:new Date().toISOString(),formType:"contact"};console.log("New contact submission received:",c);try{let e={to:o,subject:"Message Received - Emanuel Ibarra Financial Services",body:`Dear ${t},

Thank you for contacting Emanuel Ibarra Financial Services!

**Your Message:**
Subject: ${r}
Message: ${n}
Preferred Contact Method: ${i||"Email"}

We have received your inquiry and Emanuel will personally respond within 24 hours during business days.

**Emanuel's Contact Information:**
- Phone: (************* (Mon-Fri 9AM-6PM CST)
- Email: <EMAIL>
- Location: Fort Worth, TX

**Business Hours:**
- Monday - Friday: 9:00 AM - 6:00 PM
- Saturday: 10:00 AM - 2:00 PM  
- Sunday: By Appointment

For urgent matters, please don't hesitate to call directly.

Best regards,
Emanuel Ibarra
Financial Educator & Estate Planning Specialist
CEO, Hispano Financial Group LLC

---
This is an automated confirmation. Emanuel will respond personally to your inquiry soon.`},s={to:"<EMAIL>",subject:`New Contact Form Submission from ${t}`,body:`New contact form submission received:

**Contact Details:**
Name: ${t}
Email: ${o}
Phone: ${a||"Not provided"}
Preferred Contact: ${i||"Email"}

**Subject:** ${r}

**Message:**
${n}

**Submitted:** ${new Date().toLocaleString()}

Please respond within 24 hours as promised.`};console.log("Client confirmation email would be sent:",e),console.log("Emanuel notification email would be sent:",s)}catch(e){console.error("Email sending error (non-critical):",e)}return s.NextResponse.json({success:!0,message:"Thank you for your message! Emanuel will respond within 24 hours.",contactId:c.id})}catch(e){return console.error("Contact API error:",e),s.NextResponse.json({error:"Failed to submit message. Please try again or call (************* directly.",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let l=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/contact/route",pathname:"/api/contact",filename:"route",bundlePath:"app/api/contact/route"},resolvedPagePath:"/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/elgAto/emanuel/emanuel-ibarra-final-theme/app/app/api/contact/route.ts",nextConfigOutput:"export",userland:a}),{requestAsyncStorage:u,staticGenerationAsyncStorage:d,serverHooks:m}=l,p="/api/contact/route";function g(){return(0,i.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:d})}}};var t=require("../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),a=t.X(0,[276,972],()=>o(6955));module.exports=a})();