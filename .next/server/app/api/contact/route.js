"use strict";(()=>{var e={};e.id=386,e.ids=[386],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6804:(e,t,a)=>{a.r(t),a.d(t,{originalPathname:()=>y,patchFetch:()=>g,requestAsyncStorage:()=>d,routeModule:()=>u,serverHooks:()=>p,staticGenerationAsyncStorage:()=>m});var o={};a.r(o),a.d(o,{POST:()=>l,dynamic:()=>c});var r=a(9303),n=a(8716),i=a(670),s=a(7070);let c="force-dynamic";async function l(e){try{let{name:t,email:a,phone:o,subject:r,message:n,preferredContact:i}=await e.json();if(!t||!a||!r||!n)return s.NextResponse.json({error:"Missing required fields"},{status:400});let c={id:Date.now().toString(),name:t,email:a,phone:o||"",subject:r,message:n,preferredContact:i||"email",status:"new",createdAt:new Date().toISOString(),formType:"contact"};console.log("New contact submission received:",c);try{let e={to:a,subject:"Message Received - Emanuel Ibarra Financial Services",body:`Dear ${t},

Thank you for contacting Emanuel Ibarra Financial Services!

**Your Message:**
Subject: ${r}
Message: ${n}
Preferred Contact Method: ${i||"Email"}

We have received your inquiry and Emanuel will personally respond within 24 hours during business days.

**Emanuel's Contact Information:**
- Phone: (************* (Mon-Fri 9AM-6PM CST)
- Email: <EMAIL>
- Location: Fort Worth, TX

**Business Hours:**
- Monday - Friday: 9:00 AM - 6:00 PM
- Saturday: 10:00 AM - 2:00 PM  
- Sunday: By Appointment

For urgent matters, please don't hesitate to call directly.

Best regards,
Emanuel Ibarra
Financial Educator & Estate Planning Specialist
CEO, Hispano Financial Group LLC

---
This is an automated confirmation. Emanuel will respond personally to your inquiry soon.`},s={to:"<EMAIL>",subject:`New Contact Form Submission from ${t}`,body:`New contact form submission received:

**Contact Details:**
Name: ${t}
Email: ${a}
Phone: ${o||"Not provided"}
Preferred Contact: ${i||"Email"}

**Subject:** ${r}

**Message:**
${n}

**Submitted:** ${new Date().toLocaleString()}

Please respond within 24 hours as promised.`};console.log("Client confirmation email would be sent:",e),console.log("Emanuel notification email would be sent:",s)}catch(e){console.error("Email sending error (non-critical):",e)}return s.NextResponse.json({success:!0,message:"Thank you for your message! Emanuel will respond within 24 hours.",contactId:c.id})}catch(e){return console.error("Contact API error:",e),s.NextResponse.json({error:"Failed to submit message. Please try again or call (************* directly.",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let u=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/contact/route",pathname:"/api/contact",filename:"route",bundlePath:"app/api/contact/route"},resolvedPagePath:"/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/elgAto/emanuel/emanuel-ibarra-final-theme/app/app/api/contact/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:d,staticGenerationAsyncStorage:m,serverHooks:p}=u,y="/api/contact/route";function g(){return(0,i.patchFetch)({serverHooks:p,staticGenerationAsyncStorage:m})}}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[276,972],()=>a(6804));module.exports=o})();