"use strict";(()=>{var e={};e.id=17,e.ids=[17],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},918:(e,t,o)=>{o.r(t),o.d(t,{originalPathname:()=>d,patchFetch:()=>g,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>c});var n={};o.r(n),o.d(n,{POST:()=>u});var r=o(9303),a=o(8716),s=o(670),i=o(7070);async function u(e){try{let t=await e.json();return console.log("New consultation request:",{name:t.name,email:t.email,phone:t.phone,language:t.language,appointmentTime:t.appointmentTime,timestamp:new Date().toISOString()}),i.NextResponse.json({success:!0,message:"Consultation request received successfully"})}catch(e){return console.error("Consultation API error:",e),i.NextResponse.json({error:"Failed to process consultation request"},{status:500})}}let p=new r.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/consultation/route",pathname:"/api/consultation",filename:"route",bundlePath:"app/api/consultation/route"},resolvedPagePath:"/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/elgAto/emanuel/emanuel-ibarra-final-theme/app/app/api/consultation/route.ts",nextConfigOutput:"export",userland:n}),{requestAsyncStorage:l,staticGenerationAsyncStorage:c,serverHooks:m}=p,d="/api/consultation/route";function g(){return(0,s.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:c})}}};var t=require("../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),n=t.X(0,[276,972],()=>o(918));module.exports=n})();