"use strict";(()=>{var e={};e.id=744,e.ids=[744],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4354:(e,t,a)=>{a.r(t),a.d(t,{originalPathname:()=>h,patchFetch:()=>g,requestAsyncStorage:()=>p,routeModule:()=>c,serverHooks:()=>m,staticGenerationAsyncStorage:()=>d});var n={};a.r(n),a.d(n,{POST:()=>u,dynamic:()=>l});var s=a(9303),o=a(8716),r=a(670),i=a(7070);let l="force-dynamic";async function u(e){try{let{message:t,currentStep:a,userData:n,conversationHistory:s}=await e.json();if(!t||"string"!=typeof t)return i.NextResponse.json({error:"Message is required"},{status:400});let o=s?.map(e=>({role:e.isUser?"user":"assistant",content:e.text}))||[],r=`You are Emanuel Ibarra's intelligent AI assistant. You help potential clients understand Emanuel's services and guide them toward scheduling consultations.

CRITICAL: NEVER use emojis in your responses. Keep all communication professional and text-based only.

ABOUT EMANUEL:
- Financial Educator & Estate Planning Specialist
- CEO of Hispano Financial Group LLC  
- Over 10 years of financial experience
- Located in Fort Worth, TX
- Bilingual: English and Spanish
- Phone: (*************
- Email: <EMAIL>

SERVICES OFFERED:
1. Revocable Living Trusts - Avoid probate, maintain control
2. Special Needs Trusts - Protect disabled family members
3. Business Structures - LLCs, Corporations, 501(c)(3)
4. Estate Planning - Wills, Powers of Attorney
5. Financial Education - Wealth building strategies
6. Asset Protection - Shield wealth from creditors

CONVERSATION STRATEGY:
- Current step: ${a||"greeting"}
- User data collected: ${JSON.stringify(n||{})}
- Be conversational but professional
- Ask qualifying questions to understand needs
- Guide toward consultation scheduling
- Provide educational value in each response
- Always suggest the consultation is free and personalized

RESPONSE GUIDELINES:
1. NO EMOJIS - Text only, professional tone
2. Be helpful and educational
3. Ask questions to qualify prospects
4. Encourage consultation scheduling
5. Provide specific benefits of Emanuel's services
6. Use conversational, not formal language
7. Keep responses 2-3 sentences maximum
8. End with a question or clear next step

For general questions, provide educational information but always circle back to how Emanuel can help personally.`,l=await fetch("https://api.mistral.ai/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.MISTRAL_API_KEY}`},body:JSON.stringify({model:"mistral-large-latest",messages:[{role:"system",content:r},...o,{role:"user",content:t}],max_tokens:500,temperature:.7})});if(!l.ok)throw console.error("Mistral API error:",l.status,await l.text()),Error("Failed to get response from Mistral API");let u=await l.json(),c=u.choices[0]?.message?.content;if(!c)throw Error("No response from Mistral API");return i.NextResponse.json({message:c})}catch(t){console.error("Chat API error:",t);let e=`I'm here to help you learn about Emanuel's financial planning services. While I work on getting you a detailed response, here's how you can connect with Emanuel directly:

Call Emanuel: (*************
Email: <EMAIL>  
Location: Fort Worth, TX

Emanuel specializes in:
- Estate Planning & Trusts
- Business Formation & Protection
- Financial Education & Wealth Building
- Asset Protection Strategies

Emanuel offers free consultations and speaks both English and Spanish. He's available Monday through Friday 9AM-6PM for immediate assistance.`;return i.NextResponse.json({message:e})}}let c=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/chat/route",pathname:"/api/chat",filename:"route",bundlePath:"app/api/chat/route"},resolvedPagePath:"/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/elgAto/emanuel/emanuel-ibarra-final-theme/app/app/api/chat/route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:m}=c,h="/api/chat/route";function g(){return(0,r.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:d})}}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[276,972],()=>a(4354));module.exports=n})();