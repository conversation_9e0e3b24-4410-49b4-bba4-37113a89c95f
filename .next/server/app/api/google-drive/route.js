"use strict";(()=>{var e={};e.id=605,e.ids=[605],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6374:(e,a,t)=>{t.r(a),t.d(a,{originalPathname:()=>g,patchFetch:()=>h,requestAsyncStorage:()=>p,routeModule:()=>c,serverHooks:()=>m,staticGenerationAsyncStorage:()=>d});var o={};t.r(o),t.d(o,{POST:()=>u,dynamic:()=>l});var r=t(9303),n=t(8716),i=t(670),s=t(7070);let l="force-dynamic";async function u(e){try{let{email:a,documentType:t,clientName:o,documentContent:r}=await e.json();console.log("Creating and sharing PDF via Google Drive:",{email:a,documentType:t,clientName:o,timestamp:new Date().toISOString()});let n={filename:`${t}_${o}_${Date.now()}.pdf`,content:r||`
        Financial Planning Document
        
        Client: ${o}
        Document Type: ${t}
        Generated: ${new Date().toLocaleDateString()}
        
        This document contains important financial planning information
        provided by Emanuel Ibarra, Financial Educator & Estate Planning Specialist.
        
        For questions or to schedule a consultation:
        Phone: (*************
        Email: <EMAIL>
        
        Hispano Financial Group LLC
        Fort Worth, TX
      `,sharedWith:a,shareLink:`https://drive.google.com/file/d/simulated-${Date.now()}/view`};return s.NextResponse.json({success:!0,message:"PDF created and shared successfully",pdfData:n})}catch(e){return console.error("Google Drive API error:",e),s.NextResponse.json({error:"Failed to create and share PDF"},{status:500})}}let c=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/google-drive/route",pathname:"/api/google-drive",filename:"route",bundlePath:"app/api/google-drive/route"},resolvedPagePath:"/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/elgAto/emanuel/emanuel-ibarra-final-theme/app/app/api/google-drive/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:m}=c,g="/api/google-drive/route";function h(){return(0,i.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:d})}}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),o=a.X(0,[276,972],()=>t(6374));module.exports=o})();