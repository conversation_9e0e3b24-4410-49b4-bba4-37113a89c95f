"use strict";(()=>{var e={};e.id=605,e.ids=[605],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},474:(e,t,a)=>{a.r(t),a.d(t,{originalPathname:()=>g,patchFetch:()=>m,requestAsyncStorage:()=>p,routeModule:()=>u,serverHooks:()=>d,staticGenerationAsyncStorage:()=>c});var o={};a.r(o),a.d(o,{POST:()=>l});var r=a(9303),n=a(8716),i=a(670),s=a(7070);async function l(e){try{let{email:t,documentType:a,clientName:o,documentContent:r}=await e.json();console.log("Creating and sharing PDF via Google Drive:",{email:t,documentType:a,clientName:o,timestamp:new Date().toISOString()});let n={filename:`${a}_${o}_${Date.now()}.pdf`,content:r||`
        Financial Planning Document
        
        Client: ${o}
        Document Type: ${a}
        Generated: ${new Date().toLocaleDateString()}
        
        This document contains important financial planning information
        provided by Emanuel Ibarra, Financial Educator & Estate Planning Specialist.
        
        For questions or to schedule a consultation:
        Phone: (*************
        Email: <EMAIL>
        
        Hispano Financial Group LLC
        Fort Worth, TX
      `,sharedWith:t,shareLink:`https://drive.google.com/file/d/simulated-${Date.now()}/view`};return s.NextResponse.json({success:!0,message:"PDF created and shared successfully",pdfData:n})}catch(e){return console.error("Google Drive API error:",e),s.NextResponse.json({error:"Failed to create and share PDF"},{status:500})}}let u=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/google-drive/route",pathname:"/api/google-drive",filename:"route",bundlePath:"app/api/google-drive/route"},resolvedPagePath:"/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/elgAto/emanuel/emanuel-ibarra-final-theme/app/app/api/google-drive/route.ts",nextConfigOutput:"export",userland:o}),{requestAsyncStorage:p,staticGenerationAsyncStorage:c,serverHooks:d}=u,g="/api/google-drive/route";function m(){return(0,i.patchFetch)({serverHooks:d,staticGenerationAsyncStorage:c})}}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[276,972],()=>a(474));module.exports=o})();