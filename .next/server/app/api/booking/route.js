"use strict";(()=>{var e={};e.id=324,e.ids=[324],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},7778:(e,o,r)=>{r.r(o),r.d(o,{originalPathname:()=>g,patchFetch:()=>h,requestAsyncStorage:()=>d,routeModule:()=>c,serverHooks:()=>m,staticGenerationAsyncStorage:()=>p});var n={};r.r(n),r.d(n,{POST:()=>u,dynamic:()=>l});var t=r(9303),a=r(8716),i=r(670),s=r(7070);let l="force-dynamic";async function u(e){try{let{name:o,email:r,phone:n,service:t,consultationType:a,preferredDate:i,preferredTime:l,message:u}=await e.json();if(!o||!r||!n||!a)return s.NextResponse.json({error:"Missing required fields"},{status:400});let c={id:Date.now().toString(),name:o,email:r,phone:n,service:t||"General Consultation",consultationType:a,preferredDate:i||"To be scheduled",preferredTime:l||"To be scheduled",message:u||"",status:"pending",createdAt:new Date().toISOString(),formType:"booking"};console.log("New booking received:",c);try{let e={to:r,subject:"Consultation Booking Confirmation - Emanuel Ibarra Financial Services",body:`Dear ${o},

Thank you for scheduling a consultation with Emanuel Ibarra!

**Booking Details:**
- Service: ${t||"General Consultation"}
- Type: ${a}
- Preferred Date: ${i||"To be scheduled"}
- Preferred Time: ${l||"To be scheduled"}
- Phone: ${n}
- Message: ${u||"None provided"}

Emanuel will contact you within 24 hours to confirm your appointment details.

**Contact Information:**
- Phone: (*************
- Email: <EMAIL>
- Location: Fort Worth, TX

We look forward to helping you achieve your financial goals!

Best regards,
Emanuel Ibarra
Financial Educator & Estate Planning Specialist
Hispano Financial Group LLC`};console.log("Confirmation email would be sent:",e)}catch(e){console.error("Email sending error (non-critical):",e)}return s.NextResponse.json({success:!0,message:"Booking submitted successfully. Emanuel will contact you within 24 hours.",bookingId:c.id})}catch(e){return console.error("Booking API error:",e),s.NextResponse.json({error:"Failed to submit booking. Please try again or call (************* directly.",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let c=new t.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/booking/route",pathname:"/api/booking",filename:"route",bundlePath:"app/api/booking/route"},resolvedPagePath:"/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/elgAto/emanuel/emanuel-ibarra-final-theme/app/app/api/booking/route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:d,staticGenerationAsyncStorage:p,serverHooks:m}=c,g="/api/booking/route";function h(){return(0,i.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:p})}}};var o=require("../../../webpack-runtime.js");o.C(e);var r=e=>o(o.s=e),n=o.X(0,[276,972],()=>r(7778));module.exports=n})();