exports.id=71,exports.ids=[71],exports.modules={5384:e=>{e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},2881:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n=r(7577);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ");var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:s="",children:l,iconNode:u,...c},d)=>(0,n.createElement)("svg",{ref:d,...a,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:i("lucide",s),...c},[...u.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(l)?l:[l]])),l=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...a},l)=>(0,n.createElement)(s,{ref:l,iconNode:t,className:i(`lucide-${o(e)}`,r),...a}));return r.displayName=`${e}`,r}},4014:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(2881).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},748:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(2881).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},9730:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(2881).Z)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},2887:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(2881).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},4019:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(2881).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},3486:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return i}});let n=r(8974),o=r(3658);function i(e,t){return(0,o.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5424:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"callServer",{enumerable:!0,get:function(){return o}});let n=r(2994);async function o(e,t){let r=(0,n.getServerActionDispatcher)();if(!r)throw Error("Invariant: missing action dispatcher.");return new Promise((n,o)=>{r({actionId:e,actionArgs:t,resolve:n,reject:o})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return a}});let n=r(7577),o=r(962),i="next-route-announcer";function a(e){let{tree:t}=e,[r,a]=(0,n.useState)(null);(0,n.useEffect)(()=>(a(function(){var e;let t=document.getElementsByName(i)[0];if(null==t?void 0:null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(i);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(i)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[s,l]=(0,n.useState)(""),u=(0,n.useRef)();return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),r?(0,o.createPortal)(s,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5138:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION:function(){return n},FLIGHT_PARAMETERS:function(){return l},NEXT_DID_POSTPONE_HEADER:function(){return c},NEXT_ROUTER_PREFETCH_HEADER:function(){return i},NEXT_ROUTER_STATE_TREE:function(){return o},NEXT_RSC_UNION_QUERY:function(){return u},NEXT_URL:function(){return a},RSC_CONTENT_TYPE_HEADER:function(){return s},RSC_HEADER:function(){return r}});let r="RSC",n="Next-Action",o="Next-Router-State-Tree",i="Next-Router-Prefetch",a="Next-Url",s="text/x-component",l=[[r],[o],[i]],u="_rsc",c="x-nextjs-postponed";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2994:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return M},default:function(){return k},getServerActionDispatcher:function(){return R},urlToUrlWithoutFlightMarker:function(){return E}});let n=r(8374),o=r(326),i=n._(r(7577)),a=r(2413),s=r(7767),l=r(7584),u=r(7008),c=r(7326),d=r(9727),f=r(6199),h=r(2148),p=r(3486),m=r(8038),y=r(6265),g=r(2492),v=r(9519),b=r(5138),x=r(4237),P=r(7929),_=r(8071),w=null,j=null;function R(){return j}let S={};function E(e){let t=new URL(e,location.origin);if(t.searchParams.delete(b.NEXT_RSC_UNION_QUERY),t.pathname.endsWith(".txt")){let{pathname:e}=t,r=e.endsWith("/index.txt")?10:4;t.pathname=e.slice(0,-r)}return t}function T(e){return e.origin!==window.location.origin}function O(e){let{appRouterState:t,sync:r}=e;return(0,i.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:o}=t,i={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==o?(n.pendingPush=!1,window.history.pushState(i,"",o)):window.history.replaceState(i,"",o),r(t)},[t,r]),null}function M(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null}}function A(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function C(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,o=null!==n?n:r;return(0,i.useDeferredValue)(r,o)}function D(e){let t,{buildId:r,initialHead:n,initialTree:l,urlParts:d,initialSeedData:b,couldBeIntercepted:R,assetPrefix:E,missingSlots:M}=e,D=(0,i.useMemo)(()=>(0,f.createInitialRouterState)({buildId:r,initialSeedData:b,urlParts:d,initialTree:l,initialParallelRoutes:w,location:null,initialHead:n,couldBeIntercepted:R}),[r,b,d,l,n,R]),[k,N,L]=(0,c.useReducerWithReduxDevtools)(D);(0,i.useEffect)(()=>{w=null},[]);let{canonicalUrl:F}=(0,c.useUnwrapState)(k),{searchParams:U,pathname:V}=(0,i.useMemo)(()=>{let e=new URL(F,"http://n");return{searchParams:e.searchParams,pathname:(0,P.hasBasePath)(e.pathname)?(0,x.removeBasePath)(e.pathname):e.pathname}},[F]),I=(0,i.useCallback)(e=>{let{previousTree:t,serverResponse:r}=e;(0,i.startTransition)(()=>{N({type:s.ACTION_SERVER_PATCH,previousTree:t,serverResponse:r})})},[N]),B=(0,i.useCallback)((e,t,r)=>{let n=new URL((0,p.addBasePath)(e),location.href);return N({type:s.ACTION_NAVIGATE,url:n,isExternalUrl:T(n),locationSearch:location.search,shouldScroll:null==r||r,navigateType:t})},[N]);j=(0,i.useCallback)(e=>{(0,i.startTransition)(()=>{N({...e,type:s.ACTION_SERVER_ACTION})})},[N]);let z=(0,i.useMemo)(()=>({back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r;if(!(0,h.isBot)(window.navigator.userAgent)){try{r=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL.")}T(r)||(0,i.startTransition)(()=>{var e;N({type:s.ACTION_PREFETCH,url:r,kind:null!=(e=null==t?void 0:t.kind)?e:s.PrefetchKind.FULL})})}},replace:(e,t)=>{void 0===t&&(t={}),(0,i.startTransition)(()=>{var r;B(e,"replace",null==(r=t.scroll)||r)})},push:(e,t)=>{void 0===t&&(t={}),(0,i.startTransition)(()=>{var r;B(e,"push",null==(r=t.scroll)||r)})},refresh:()=>{(0,i.startTransition)(()=>{N({type:s.ACTION_REFRESH,origin:window.location.origin})})},fastRefresh:()=>{throw Error("fastRefresh can only be used in development mode. Please use refresh instead.")}}),[N,B]);(0,i.useEffect)(()=>{window.next&&(window.next.router=z)},[z]),(0,i.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(S.pendingMpaPath=void 0,N({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[N]);let{pushRef:H}=(0,c.useUnwrapState)(k);if(H.mpaNavigation){if(S.pendingMpaPath!==F){let e=window.location;H.pendingPush?e.assign(F):e.replace(F),S.pendingMpaPath=F}(0,i.use)(v.unresolvedThenable)}(0,i.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,i.startTransition)(()=>{N({type:s.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,o){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=A(t),o&&r(o)),e(t,n,o)},window.history.replaceState=function(e,n,o){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=A(e),o&&r(o)),t(e,n,o)};let n=e=>{let{state:t}=e;if(t){if(!t.__NA){window.location.reload();return}(0,i.startTransition)(()=>{N({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:t.__PRIVATE_NEXTJS_INTERNALS_TREE})})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[N]);let{cache:W,tree:$,nextUrl:G,focusAndScrollRef:K}=(0,c.useUnwrapState)(k),Y=(0,i.useMemo)(()=>(0,g.findHeadInCache)(W,$[1]),[W,$]),X=(0,i.useMemo)(()=>(function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],o=Array.isArray(t),i=o?t[1]:t;!i||i.startsWith(_.PAGE_SEGMENT_KEY)||(o&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):o&&(r[t[0]]=t[1]),r=e(n,r))}return r})($),[$]);if(null!==Y){let[e,r]=Y;t=(0,o.jsx)(C,{headCacheNode:e},r)}else t=null;let Z=(0,o.jsxs)(y.RedirectBoundary,{children:[t,W.rsc,(0,o.jsx)(m.AppRouterAnnouncer,{tree:$})]});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(O,{appRouterState:(0,c.useUnwrapState)(k),sync:L}),(0,o.jsx)(u.PathParamsContext.Provider,{value:X,children:(0,o.jsx)(u.PathnameContext.Provider,{value:V,children:(0,o.jsx)(u.SearchParamsContext.Provider,{value:U,children:(0,o.jsx)(a.GlobalLayoutRouterContext.Provider,{value:{buildId:r,changeByServerResponse:I,tree:$,focusAndScrollRef:K,nextUrl:G},children:(0,o.jsx)(a.AppRouterContext.Provider,{value:z,children:(0,o.jsx)(a.LayoutRouterContext.Provider,{value:{childNodes:W.parallelRoutes,tree:$,url:F,loading:W.loading},children:Z})})})})})})]})}function k(e){let{globalErrorComponent:t,...r}=e;return(0,o.jsx)(d.ErrorBoundary,{errorComponent:t,children:(0,o.jsx)(D,{...r})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6136:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"bailoutToClientRendering",{enumerable:!0,get:function(){return i}});let n=r(4129),o=r(5869);function i(e){let t=o.staticGenerationAsyncStorage.getStore();if((null==t||!t.forceStatic)&&(null==t?void 0:t.isStaticGeneration))throw new n.BailoutToCSRError(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6114:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ClientPageRoot",{enumerable:!0,get:function(){return i}});let n=r(326),o=r(3325);function i(e){let{Component:t,props:r}=e;return r.searchParams=(0,o.createDynamicallyTrackedSearchParams)(r.searchParams||{}),(0,n.jsx)(t,{...r})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9727:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ErrorBoundary:function(){return p},ErrorBoundaryHandler:function(){return d},GlobalError:function(){return f},default:function(){return h}});let n=r(1174),o=r(326),i=n._(r(7577)),a=r(7389),s=r(7313),l=r(5869),u={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}};function c(e){let{error:t}=e,r=l.staticGenerationAsyncStorage.getStore();if((null==r?void 0:r.isRevalidate)||(null==r?void 0:r.isStaticGeneration))throw console.error(t),t;return null}class d extends i.default.Component{static getDerivedStateFromError(e){if((0,s.isNextRouterError)(e))throw e;return{error:e}}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.error?{error:null,previousPathname:e.pathname}:{error:t.error,previousPathname:e.pathname}}render(){return this.state.error?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(c,{error:this.state.error}),this.props.errorStyles,this.props.errorScripts,(0,o.jsx)(this.props.errorComponent,{error:this.state.error,reset:this.reset})]}):this.props.children}constructor(e){super(e),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}}function f(e){let{error:t}=e,r=null==t?void 0:t.digest;return(0,o.jsxs)("html",{id:"__next_error__",children:[(0,o.jsx)("head",{}),(0,o.jsxs)("body",{children:[(0,o.jsx)(c,{error:t}),(0,o.jsx)("div",{style:u.error,children:(0,o.jsxs)("div",{children:[(0,o.jsx)("h2",{style:u.text,children:"Application error: a "+(r?"server":"client")+"-side exception has occurred (see the "+(r?"server logs":"browser console")+" for more information)."}),r?(0,o.jsx)("p",{style:u.text,children:"Digest: "+r}):null]})})]})]})}let h=f;function p(e){let{errorComponent:t,errorStyles:r,errorScripts:n,children:i}=e,s=(0,a.usePathname)();return t?(0,o.jsx)(d,{pathname:s,errorComponent:t,errorStyles:r,errorScripts:n,children:i}):(0,o.jsx)(o.Fragment,{children:i})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},442:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DynamicServerError:function(){return n},isDynamicServerError:function(){return o}});let r="DYNAMIC_SERVER_USAGE";class n extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=r}}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7313:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return i}});let n=r(706),o=r(2747);function i(e){return e&&e.digest&&((0,o.isRedirectError)(e)||(0,n.isNotFoundError)(e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9671:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return w}}),r(1174);let n=r(8374),o=r(326),i=n._(r(7577));r(962);let a=r(2413),s=r(9009),l=r(9519),u=r(9727),c=r(455),d=r(9976),f=r(6265),h=r(1868),p=r(2162),m=r(9886),y=r(5262),g=["bottom","height","left","right","top","width","x","y"];function v(e,t){let r=e.getBoundingClientRect();return r.top>=0&&r.top<=t}class b extends i.default.Component{componentDidMount(){this.handlePotentialScroll()}componentDidUpdate(){this.props.focusAndScrollRef.apply&&this.handlePotentialScroll()}render(){return this.props.children}constructor(...e){super(...e),this.handlePotentialScroll=()=>{let{focusAndScrollRef:e,segmentPath:t}=this.props;if(e.apply){if(0!==e.segmentPaths.length&&!e.segmentPaths.some(e=>t.every((t,r)=>(0,c.matchSegment)(t,e[r]))))return;let r=null,n=e.hashFragment;if(n&&(r=function(e){var t;return"top"===e?document.body:null!=(t=document.getElementById(e))?t:document.getElementsByName(e)[0]}(n)),!r&&(r=null),!(r instanceof Element))return;for(;!(r instanceof HTMLElement)||function(e){if(["sticky","fixed"].includes(getComputedStyle(e).position))return!0;let t=e.getBoundingClientRect();return g.every(e=>0===t[e])}(r);){if(null===r.nextElementSibling)return;r=r.nextElementSibling}e.apply=!1,e.hashFragment=null,e.segmentPaths=[],(0,d.handleSmoothScroll)(()=>{if(n){r.scrollIntoView();return}let e=document.documentElement,t=e.clientHeight;!v(r,t)&&(e.scrollTop=0,v(r,t)||r.scrollIntoView())},{dontForceLayout:!0,onlyHashChange:e.onlyHashChange}),e.onlyHashChange=!1,r.focus()}}}}function x(e){let{segmentPath:t,children:r}=e,n=(0,i.useContext)(a.GlobalLayoutRouterContext);if(!n)throw Error("invariant global layout router not mounted");return(0,o.jsx)(b,{segmentPath:t,focusAndScrollRef:n.focusAndScrollRef,children:r})}function P(e){let{parallelRouterKey:t,url:r,childNodes:n,segmentPath:u,tree:d,cacheKey:f}=e,h=(0,i.useContext)(a.GlobalLayoutRouterContext);if(!h)throw Error("invariant global layout router not mounted");let{buildId:p,changeByServerResponse:m,tree:g}=h,v=n.get(f);if(void 0===v){let e={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null};v=e,n.set(f,e)}let b=null!==v.prefetchRsc?v.prefetchRsc:v.rsc,x=(0,i.useDeferredValue)(v.rsc,b),P="object"==typeof x&&null!==x&&"function"==typeof x.then?(0,i.use)(x):x;if(!P){let e=v.lazyData;if(null===e){let t=function e(t,r){if(t){let[n,o]=t,i=2===t.length;if((0,c.matchSegment)(r[0],n)&&r[1].hasOwnProperty(o)){if(i){let t=e(void 0,r[1][o]);return[r[0],{...r[1],[o]:[t[0],t[1],t[2],"refetch"]}]}return[r[0],{...r[1],[o]:e(t.slice(2),r[1][o])}]}}return r}(["",...u],g),n=(0,y.hasInterceptionRouteInCurrentTree)(g);v.lazyData=e=(0,s.fetchServerResponse)(new URL(r,location.origin),t,n?h.nextUrl:null,p),v.lazyDataResolved=!1}let t=(0,i.use)(e);v.lazyDataResolved||(setTimeout(()=>{(0,i.startTransition)(()=>{m({previousTree:g,serverResponse:t})})}),v.lazyDataResolved=!0),(0,i.use)(l.unresolvedThenable)}return(0,o.jsx)(a.LayoutRouterContext.Provider,{value:{tree:d[1][t],childNodes:v.parallelRoutes,url:r,loading:v.loading},children:P})}function _(e){let{children:t,hasLoading:r,loading:n,loadingStyles:a,loadingScripts:s}=e;return r?(0,o.jsx)(i.Suspense,{fallback:(0,o.jsxs)(o.Fragment,{children:[a,s,n]}),children:t}):(0,o.jsx)(o.Fragment,{children:t})}function w(e){let{parallelRouterKey:t,segmentPath:r,error:n,errorStyles:s,errorScripts:l,templateStyles:c,templateScripts:d,template:y,notFound:g,notFoundStyles:v}=e,b=(0,i.useContext)(a.LayoutRouterContext);if(!b)throw Error("invariant expected layout router to be mounted");let{childNodes:w,tree:j,url:R,loading:S}=b,E=w.get(t);E||(E=new Map,w.set(t,E));let T=j[1][t][0],O=(0,p.getSegmentValue)(T),M=[T];return(0,o.jsx)(o.Fragment,{children:M.map(e=>{let i=(0,p.getSegmentValue)(e),b=(0,m.createRouterCacheKey)(e);return(0,o.jsxs)(a.TemplateContext.Provider,{value:(0,o.jsx)(x,{segmentPath:r,children:(0,o.jsx)(u.ErrorBoundary,{errorComponent:n,errorStyles:s,errorScripts:l,children:(0,o.jsx)(_,{hasLoading:!!S,loading:null==S?void 0:S[0],loadingStyles:null==S?void 0:S[1],loadingScripts:null==S?void 0:S[2],children:(0,o.jsx)(h.NotFoundBoundary,{notFound:g,notFoundStyles:v,children:(0,o.jsx)(f.RedirectBoundary,{children:(0,o.jsx)(P,{parallelRouterKey:t,url:R,tree:j,childNodes:E,segmentPath:r,cacheKey:b,isActive:O===i})})})})})}),children:[c,d,y]},(0,m.createRouterCacheKey)(e,!0))})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},455:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{canSegmentBeOverridden:function(){return i},matchSegment:function(){return o}});let n=r(2357),o=(e,t)=>"string"==typeof e?"string"==typeof t&&e===t:"string"!=typeof t&&e[0]===t[0]&&e[1]===t[1],i=(e,t)=>{var r;return!Array.isArray(e)&&!!Array.isArray(t)&&(null==(r=(0,n.getSegmentParam)(e))?void 0:r.param)===t[0]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7389:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return l.ReadonlyURLSearchParams},RedirectType:function(){return l.RedirectType},ServerInsertedHTMLContext:function(){return u.ServerInsertedHTMLContext},notFound:function(){return l.notFound},permanentRedirect:function(){return l.permanentRedirect},redirect:function(){return l.redirect},useParams:function(){return h},usePathname:function(){return d},useRouter:function(){return f},useSearchParams:function(){return c},useSelectedLayoutSegment:function(){return m},useSelectedLayoutSegments:function(){return p},useServerInsertedHTML:function(){return u.useServerInsertedHTML}});let n=r(7577),o=r(2413),i=r(7008),a=r(2162),s=r(8071),l=r(7375),u=r(3347);function c(){let e=(0,n.useContext)(i.SearchParamsContext),t=(0,n.useMemo)(()=>e?new l.ReadonlyURLSearchParams(e):null,[e]);{let{bailoutToClientRendering:e}=r(6136);e("useSearchParams()")}return t}function d(){return(0,n.useContext)(i.PathnameContext)}function f(){let e=(0,n.useContext)(o.AppRouterContext);if(null===e)throw Error("invariant expected app router to be mounted");return e}function h(){return(0,n.useContext)(i.PathParamsContext)}function p(e){void 0===e&&(e="children");let t=(0,n.useContext)(o.LayoutRouterContext);return t?function e(t,r,n,o){let i;if(void 0===n&&(n=!0),void 0===o&&(o=[]),n)i=t[1][r];else{var l;let e=t[1];i=null!=(l=e.children)?l:Object.values(e)[0]}if(!i)return o;let u=i[0],c=(0,a.getSegmentValue)(u);return!c||c.startsWith(s.PAGE_SEGMENT_KEY)?o:(o.push(c),e(i,r,!1,o))}(t.tree,e):null}function m(e){void 0===e&&(e="children");let t=p(e);if(!t||0===t.length)return null;let r="children"===e?t[0]:t[t.length-1];return r===s.DEFAULT_SEGMENT_KEY?null:r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7375:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return a},RedirectType:function(){return n.RedirectType},notFound:function(){return o.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(2747),o=r(706);class i extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class a extends URLSearchParams{append(){throw new i}delete(){throw new i}set(){throw new i}sort(){throw new i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1868:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NotFoundBoundary",{enumerable:!0,get:function(){return c}});let n=r(8374),o=r(326),i=n._(r(7577)),a=r(7389),s=r(706);r(576);let l=r(2413);class u extends i.default.Component{componentDidCatch(){}static getDerivedStateFromError(e){if((0,s.isNotFoundError)(e))return{notFoundTriggered:!0};throw e}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.notFoundTriggered?{notFoundTriggered:!1,previousPathname:e.pathname}:{notFoundTriggered:t.notFoundTriggered,previousPathname:e.pathname}}render(){return this.state.notFoundTriggered?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("meta",{name:"robots",content:"noindex"}),!1,this.props.notFoundStyles,this.props.notFound]}):this.props.children}constructor(e){super(e),this.state={notFoundTriggered:!!e.asNotFound,previousPathname:e.pathname}}}function c(e){let{notFound:t,notFoundStyles:r,asNotFound:n,children:s}=e,c=(0,a.usePathname)(),d=(0,i.useContext)(l.MissingSlotContext);return t?(0,o.jsx)(u,{pathname:c,notFound:t,notFoundStyles:r,asNotFound:n,missingSlots:d,children:s}):(0,o.jsx)(o.Fragment,{children:s})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},706:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return o},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7815:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let n=r(8285),o=r(8817);var i=o._("_maxConcurrency"),a=o._("_runningCount"),s=o._("_queue"),l=o._("_processNext");class u{enqueue(e){let t,r;let o=new Promise((e,n)=>{t=e,r=n}),i=async()=>{try{n._(this,a)[a]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,a)[a]--,n._(this,l)[l]()}};return n._(this,s)[s].push({promiseFn:o,task:i}),n._(this,l)[l](),o}bump(e){let t=n._(this,s)[s].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,s)[s].splice(t,1)[0];n._(this,s)[s].unshift(e),n._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,i,{writable:!0,value:void 0}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),n._(this,i)[i]=e,n._(this,a)[a]=0,n._(this,s)[s]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,a)[a]<n._(this,i)[i]||e)&&n._(this,s)[s].length>0){var t;null==(t=n._(this,s)[s].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6265:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectBoundary:function(){return c},RedirectErrorBoundary:function(){return u}});let n=r(8374),o=r(326),i=n._(r(7577)),a=r(7389),s=r(2747);function l(e){let{redirect:t,reset:r,redirectType:n}=e,o=(0,a.useRouter)();return(0,i.useEffect)(()=>{i.default.startTransition(()=>{n===s.RedirectType.push?o.push(t,{}):o.replace(t,{}),r()})},[t,n,r,o]),null}class u extends i.default.Component{static getDerivedStateFromError(e){if((0,s.isRedirectError)(e))return{redirect:(0,s.getURLFromRedirectError)(e),redirectType:(0,s.getRedirectTypeFromError)(e)};throw e}render(){let{redirect:e,redirectType:t}=this.state;return null!==e&&null!==t?(0,o.jsx)(l,{redirect:e,redirectType:t,reset:()=>this.setState({redirect:null})}):this.props.children}constructor(e){super(e),this.state={redirect:null,redirectType:null}}}function c(e){let{children:t}=e,r=(0,a.useRouter)();return(0,o.jsx)(u,{router:r,children:t})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8778:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2747:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return l},getRedirectStatusCodeFromError:function(){return p},getRedirectTypeFromError:function(){return h},getURLFromRedirectError:function(){return f},isRedirectError:function(){return d},permanentRedirect:function(){return c},redirect:function(){return u}});let o=r(4580),i=r(2934),a=r(8778),s="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=a.RedirectStatusCode.TemporaryRedirect);let n=Error(s);n.digest=s+";"+t+";"+e+";"+r+";";let i=o.requestAsyncStorage.getStore();return i&&(n.mutableCookies=i.mutableCookies),n}function u(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.PermanentRedirect)}function d(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,o]=e.digest.split(";",4),i=Number(o);return t===s&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(i)&&i in a.RedirectStatusCode}function f(e){return d(e)?e.digest.split(";",3)[2]:null}function h(e){if(!d(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function p(e){if(!d(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let n=r(8374),o=r(326),i=n._(r(7577)),a=r(2413);function s(){let e=(0,i.useContext)(a.TemplateContext);return(0,o.jsx)(o.Fragment,{children:e})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9894:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return i}});let n=r(114),o=r(9056);function i(e,t,r,i){let[a,s,l]=r.slice(-3);if(null===s)return!1;if(3===r.length){let r=s[2],o=s[3];t.loading=o,t.rsc=r,t.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(t,e,a,s,l,i)}else t.rsc=e.rsc,t.prefetchRsc=e.prefetchRsc,t.parallelRoutes=new Map(e.parallelRoutes),t.loading=e.loading,(0,o.fillCacheWithNewSubTreeData)(t,e,r,i);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5166:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,s){let l;let[u,c,d,f,h]=r;if(1===t.length){let e=a(r,n,t);return(0,i.addRefreshMarkerToActiveParallelSegments)(e,s),e}let[p,m]=t;if(!(0,o.matchSegment)(p,u))return null;if(2===t.length)l=a(c[m],n,t);else if(null===(l=e(t.slice(2),c[m],n,s)))return null;let y=[t[0],{...c,[m]:l},d,f];return h&&(y[4]=!0),(0,i.addRefreshMarkerToActiveParallelSegments)(y,s),y}}});let n=r(8071),o=r(455),i=r(4158);function a(e,t,r){let[i,s]=e,[l,u]=t;if(l===n.DEFAULT_SEGMENT_KEY&&i!==n.DEFAULT_SEGMENT_KEY)return e;if((0,o.matchSegment)(i,l)){let t={};for(let e in s)void 0!==u[e]?t[e]=a(s[e],u[e],r):t[e]=s[e];for(let e in u)t[e]||(t[e]=u[e]);let n=[i,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2895:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,o){let i=o.length<=2,[a,s]=o,l=(0,n.createRouterCacheKey)(s),u=r.parallelRoutes.get(a),c=t.parallelRoutes.get(a);c&&c!==u||(c=new Map(u),t.parallelRoutes.set(a,c));let d=null==u?void 0:u.get(l),f=c.get(l);if(i){f&&f.lazyData&&f!==d||c.set(l,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null});return}if(!f||!d){f||c.set(l,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null});return}return f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),lazyDataResolved:f.lazyDataResolved,loading:f.loading},c.set(l,f)),e(f,d,o.slice(2))}}});let n=r(9886);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3648:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u}});let n=r(7356),o=r(8071),i=r(455),a=e=>"/"===e[0]?e.slice(1):e,s=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=a(t))||(0,o.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===o.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(o.PAGE_SEGMENT_KEY))return"";let i=[s(r)],a=null!=(t=e[1])?t:{},c=a.children?u(a.children):void 0;if(void 0!==c)i.push(c);else for(let[e,t]of Object.entries(a)){if("children"===e)continue;let r=u(t);void 0!==r&&i.push(r)}return l(i)}function c(e,t){let r=function e(t,r){let[o,a]=t,[l,c]=r,d=s(o),f=s(l);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||f.startsWith(e)))return"";if(!(0,i.matchSegment)(o,l)){var h;return null!=(h=u(r))?h:""}for(let t in a)if(c[t]){let r=e(a[t],c[t]);if(null!==r)return s(l)+"/"+r}return null}(e,t);return null==r||"/"===r?r:l(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7584:(e,t)=>{"use strict";function r(e,t){return void 0===t&&(t=!0),e.pathname+e.search+(t?e.hash:"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createHrefFromUrl",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6199:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createInitialRouterState",{enumerable:!0,get:function(){return u}});let n=r(7584),o=r(114),i=r(3648),a=r(9373),s=r(7767),l=r(4158);function u(e){var t;let{buildId:r,initialTree:u,initialSeedData:c,urlParts:d,initialParallelRoutes:f,location:h,initialHead:p,couldBeIntercepted:m}=e,y=d.join("/"),g=!h,v={lazyData:null,rsc:c[2],prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:g?new Map:f,lazyDataResolved:!1,loading:c[3]},b=h?(0,n.createHrefFromUrl)(h):y;(0,l.addRefreshMarkerToActiveParallelSegments)(u,b);let x=new Map;(null===f||0===f.size)&&(0,o.fillLazyItemsTillLeafWithHead)(v,void 0,u,c,p);let P={buildId:r,tree:u,cache:v,prefetchCache:x,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:{apply:!1,onlyHashChange:!1,hashFragment:null,segmentPaths:[]},canonicalUrl:b,nextUrl:null!=(t=(0,i.extractPathFromFlightRouterState)(u)||(null==h?void 0:h.pathname))?t:null};if(h){let e=new URL(""+h.pathname+h.search,h.origin),t=[["",u,null,null]];(0,a.createPrefetchCacheEntryForInitialLoad)({url:e,kind:s.PrefetchKind.AUTO,data:[t,void 0,!1,m],tree:P.tree,prefetchCache:P.prefetchCache,nextUrl:P.nextUrl})}return P}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9886:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createRouterCacheKey",{enumerable:!0,get:function(){return o}});let n=r(8071);function o(e,t){return(void 0===t&&(t=!1),Array.isArray(e))?e[0]+"|"+e[1]+"|"+e[2]:t&&e.startsWith(n.PAGE_SEGMENT_KEY)?n.PAGE_SEGMENT_KEY:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9009:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fetchServerResponse",{enumerable:!0,get:function(){return c}});let n=r(5138),o=r(2994),i=r(5424),a=r(7767),s=r(2165),{createFromFetch:l}=r(6493);function u(e){return[(0,o.urlToUrlWithoutFlightMarker)(e).toString(),void 0,!1,!1]}async function c(e,t,r,c,d){let f={[n.RSC_HEADER]:"1",[n.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(t))};d===a.PrefetchKind.AUTO&&(f[n.NEXT_ROUTER_PREFETCH_HEADER]="1"),r&&(f[n.NEXT_URL]=r);let h=(0,s.hexHash)([f[n.NEXT_ROUTER_PREFETCH_HEADER]||"0",f[n.NEXT_ROUTER_STATE_TREE],f[n.NEXT_URL]].join(","));try{var p;let t=new URL(e);t.pathname.endsWith("/")?t.pathname+="index.txt":t.pathname+=".txt",t.searchParams.set(n.NEXT_RSC_UNION_QUERY,h);let r=await fetch(t,{credentials:"same-origin",headers:f}),a=(0,o.urlToUrlWithoutFlightMarker)(r.url),s=r.redirected?a:void 0,d=r.headers.get("content-type")||"",m=!!r.headers.get(n.NEXT_DID_POSTPONE_HEADER),y=!!(null==(p=r.headers.get("vary"))?void 0:p.includes(n.NEXT_URL)),g=d===n.RSC_CONTENT_TYPE_HEADER;if(g||(g=d.startsWith("text/plain")),!g||!r.ok)return e.hash&&(a.hash=e.hash),u(a.toString());let[v,b]=await l(Promise.resolve(r),{callServer:i.callServer});if(c!==v)return u(r.url);return[b,s,m,y]}catch(t){return console.error("Failed to fetch RSC payload for "+e+". Falling back to browser navigation.",t),[e.toString(),void 0,!1,!1]}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9056:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillCacheWithNewSubTreeData",{enumerable:!0,get:function(){return function e(t,r,a,s){let l=a.length<=5,[u,c]=a,d=(0,i.createRouterCacheKey)(c),f=r.parallelRoutes.get(u);if(!f)return;let h=t.parallelRoutes.get(u);h&&h!==f||(h=new Map(f),t.parallelRoutes.set(u,h));let p=f.get(d),m=h.get(d);if(l){if(!m||!m.lazyData||m===p){let e=a[3];m={lazyData:null,rsc:e[2],prefetchRsc:null,head:null,prefetchHead:null,loading:e[3],parallelRoutes:p?new Map(p.parallelRoutes):new Map,lazyDataResolved:!1},p&&(0,n.invalidateCacheByRouterState)(m,p,a[2]),(0,o.fillLazyItemsTillLeafWithHead)(m,p,a[2],e,a[4],s),h.set(d,m)}return}m&&p&&(m===p&&(m={lazyData:m.lazyData,rsc:m.rsc,prefetchRsc:m.prefetchRsc,head:m.head,prefetchHead:m.prefetchHead,parallelRoutes:new Map(m.parallelRoutes),lazyDataResolved:!1,loading:m.loading},h.set(d,m)),e(m,p,a.slice(2),s))}}});let n=r(2498),o=r(114),i=r(9886);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},114:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,i,a,s,l){if(0===Object.keys(i[1]).length){t.head=s;return}for(let u in i[1]){let c;let d=i[1][u],f=d[0],h=(0,n.createRouterCacheKey)(f),p=null!==a&&void 0!==a[1][u]?a[1][u]:null;if(r){let n=r.parallelRoutes.get(u);if(n){let r;let i=(null==l?void 0:l.kind)==="auto"&&l.status===o.PrefetchCacheEntryStatus.reusable,a=new Map(n),c=a.get(h);r=null!==p?{lazyData:null,rsc:p[2],prefetchRsc:null,head:null,prefetchHead:null,loading:p[3],parallelRoutes:new Map(null==c?void 0:c.parallelRoutes),lazyDataResolved:!1}:i&&c?{lazyData:c.lazyData,rsc:c.rsc,prefetchRsc:c.prefetchRsc,head:c.head,prefetchHead:c.prefetchHead,parallelRoutes:new Map(c.parallelRoutes),lazyDataResolved:c.lazyDataResolved,loading:c.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==c?void 0:c.parallelRoutes),lazyDataResolved:!1,loading:null},a.set(h,r),e(r,c,d,p||null,s,l),t.parallelRoutes.set(u,a);continue}}if(null!==p){let e=p[2],t=p[3];c={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null};let m=t.parallelRoutes.get(u);m?m.set(h,c):t.parallelRoutes.set(u,new Map([[h,c]])),e(c,void 0,d,p,s,l)}}}});let n=r(9886),o=r(7767);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7252:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return i}});let n=r(3648);function o(e){return void 0!==e}function i(e,t){var r,i,a;let s=null==(i=t.shouldScroll)||i,l=e.nextUrl;if(o(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?l=r:l||(l=e.canonicalUrl)}return{buildId:e.buildId,canonicalUrl:o(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:o(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:o(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:o(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!s&&(!!o(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:!!t.hashFragment&&e.canonicalUrl.split("#",1)[0]===(null==(r=t.canonicalUrl)?void 0:r.split("#",1)[0]),hashFragment:s?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:s?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:o(t.patchedTree)?t.patchedTree:e.tree,nextUrl:l}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5652:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return o}});let n=r(941);function o(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3193:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,o){let i=o.length<=2,[a,s]=o,l=(0,n.createRouterCacheKey)(s),u=r.parallelRoutes.get(a);if(!u)return;let c=t.parallelRoutes.get(a);if(c&&c!==u||(c=new Map(u),t.parallelRoutes.set(a,c)),i){c.delete(l);return}let d=u.get(l),f=c.get(l);f&&d&&(f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),lazyDataResolved:f.lazyDataResolved},c.set(l,f)),e(f,d,o.slice(2)))}}});let n=r(9886);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2498:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return o}});let n=r(9886);function o(e,t,r){for(let o in r[1]){let i=r[1][o][0],a=(0,n.createRouterCacheKey)(i),s=t.parallelRoutes.get(o);if(s){let t=new Map(s);t.delete(a),e.parallelRoutes.set(o,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3772:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],o=r[0];if(Array.isArray(n)&&Array.isArray(o)){if(n[0]!==o[0]||n[2]!==o[2])return!0}else if(n!==o)return!0;if(t[4])return!r[4];if(r[4])return!0;let i=Object.values(t[1])[0],a=Object.values(r[1])[0];return!i||!a||e(i,a)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8831:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return u},listenForDynamicRequest:function(){return s},updateCacheNodeOnNavigation:function(){return function e(t,r,s,u,c){let d=r[1],f=s[1],h=u[1],p=t.parallelRoutes,m=new Map(p),y={},g=null;for(let t in f){let r;let s=f[t],u=d[t],v=p.get(t),b=h[t],x=s[0],P=(0,i.createRouterCacheKey)(x),_=void 0!==u?u[0]:void 0,w=void 0!==v?v.get(P):void 0;if(null!==(r=x===n.PAGE_SEGMENT_KEY?a(s,void 0!==b?b:null,c):x===n.DEFAULT_SEGMENT_KEY?void 0!==u?{route:u,node:null,children:null}:a(s,void 0!==b?b:null,c):void 0!==_&&(0,o.matchSegment)(x,_)&&void 0!==w&&void 0!==u?null!=b?e(w,u,s,b,c):function(e){let t=l(e,null,null);return{route:e,node:t,children:null}}(s):a(s,void 0!==b?b:null,c))){null===g&&(g=new Map),g.set(t,r);let e=r.node;if(null!==e){let r=new Map(v);r.set(P,e),m.set(t,r)}y[t]=r.route}else y[t]=s}if(null===g)return null;let v={lazyData:null,rsc:t.rsc,prefetchRsc:t.prefetchRsc,head:t.head,prefetchHead:t.prefetchHead,loading:t.loading,parallelRoutes:m,lazyDataResolved:!1};return{route:function(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}(s,y),node:v,children:g}}},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],o=t.parallelRoutes,a=new Map(o);for(let t in n){let r=n[t],s=r[0],l=(0,i.createRouterCacheKey)(s),u=o.get(t);if(void 0!==u){let n=u.get(l);if(void 0!==n){let o=e(n,r),i=new Map(u);i.set(l,o),a.set(t,i)}}}let s=t.rsc,l=f(s)&&"pending"===s.status;return{lazyData:null,rsc:s,head:t.head,prefetchHead:l?t.prefetchHead:null,prefetchRsc:l?t.prefetchRsc:null,loading:l?t.loading:null,parallelRoutes:a,lazyDataResolved:!1}}}});let n=r(8071),o=r(455),i=r(9886);function a(e,t,r){let n=l(e,t,r);return{route:e,node:n,children:null}}function s(e,t){t.then(t=>{for(let r of t[0]){let t=r.slice(0,-3),n=r[r.length-3],a=r[r.length-2],s=r[r.length-1];"string"!=typeof t&&function(e,t,r,n,a){let s=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],i=s.children;if(null!==i){let e=i.get(r);if(void 0!==e){let t=e.route[0];if((0,o.matchSegment)(n,t)){s=e;continue}}}return}(function e(t,r,n,a){let s=t.children,l=t.node;if(null===s){null!==l&&(function e(t,r,n,a,s){let l=r[1],u=n[1],d=a[1],h=t.parallelRoutes;for(let t in l){let r=l[t],n=u[t],a=d[t],f=h.get(t),p=r[0],m=(0,i.createRouterCacheKey)(p),y=void 0!==f?f.get(m):void 0;void 0!==y&&(void 0!==n&&(0,o.matchSegment)(p,n[0])&&null!=a?e(y,r,n,a,s):c(r,y,null))}let p=t.rsc,m=a[2];null===p?t.rsc=m:f(p)&&p.resolve(m);let y=t.head;f(y)&&y.resolve(s)}(l,t.route,r,n,a),t.node=null);return}let u=r[1],d=n[1];for(let t in r){let r=u[t],n=d[t],i=s.get(t);if(void 0!==i){let t=i.route[0];if((0,o.matchSegment)(r[0],t)&&null!=n)return e(i,r,n,a)}}})(s,r,n,a)}(e,t,n,a,s)}u(e,null)},t=>{u(e,t)})}function l(e,t,r){let n=e[1],o=null!==t?t[1]:null,a=new Map;for(let e in n){let t=n[e],s=null!==o?o[e]:null,u=t[0],c=(0,i.createRouterCacheKey)(u),d=l(t,void 0===s?null:s,r),f=new Map;f.set(c,d),a.set(e,f)}let s=0===a.size,u=null!==t?t[2]:null,c=null!==t?t[3]:null;return{lazyData:null,parallelRoutes:a,prefetchRsc:void 0!==u?u:null,prefetchHead:s?r:null,loading:void 0!==c?c:null,rsc:h(),head:s?h():null,lazyDataResolved:!1}}function u(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)c(e.route,r,t);else for(let e of n.values())u(e,t);e.node=null}function c(e,t,r){let n=e[1],o=t.parallelRoutes;for(let e in n){let t=n[e],a=o.get(e);if(void 0===a)continue;let s=t[0],l=(0,i.createRouterCacheKey)(s),u=a.get(l);void 0!==u&&c(t,u,r)}let a=t.rsc;f(a)&&(null===r?a.resolve(null):a.reject(r));let s=t.head;f(s)&&s.resolve(null)}let d=Symbol();function f(e){return e&&e.tag===d}function h(){let e,t;let r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=d,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9373:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createPrefetchCacheEntryForInitialLoad:function(){return u},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return d}});let n=r(7584),o=r(9009),i=r(7767),a=r(1156);function s(e,t){let r=(0,n.createHrefFromUrl)(e,!1);return t?t+"%"+r:r}function l(e){let t,{url:r,nextUrl:n,tree:o,buildId:a,prefetchCache:l,kind:u}=e,d=s(r,n),f=l.get(d);if(f)t=f;else{let e=s(r),n=l.get(e);n&&(t=n)}return t?(t.status=p(t),t.kind!==i.PrefetchKind.FULL&&u===i.PrefetchKind.FULL)?c({tree:o,url:r,buildId:a,nextUrl:n,prefetchCache:l,kind:null!=u?u:i.PrefetchKind.TEMPORARY}):(u&&t.kind===i.PrefetchKind.TEMPORARY&&(t.kind=u),t):c({tree:o,url:r,buildId:a,nextUrl:n,prefetchCache:l,kind:u||i.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:r,prefetchCache:n,url:o,kind:a,data:l}=e,[,,,u]=l,c=u?s(o,t):s(o),d={treeAtTimeOfPrefetch:r,data:Promise.resolve(l),kind:a,prefetchTime:Date.now(),lastUsedTime:Date.now(),key:c,status:i.PrefetchCacheEntryStatus.fresh};return n.set(c,d),d}function c(e){let{url:t,kind:r,tree:n,nextUrl:l,buildId:u,prefetchCache:c}=e,d=s(t),f=a.prefetchQueue.enqueue(()=>(0,o.fetchServerResponse)(t,n,l,u,r).then(e=>{let[,,,r]=e;return r&&function(e){let{url:t,nextUrl:r,prefetchCache:n}=e,o=s(t),i=n.get(o);if(!i)return;let a=s(t,r);n.set(a,i),n.delete(o)}({url:t,nextUrl:l,prefetchCache:c}),e})),h={treeAtTimeOfPrefetch:n,data:f,kind:r,prefetchTime:Date.now(),lastUsedTime:null,key:d,status:i.PrefetchCacheEntryStatus.fresh};return c.set(d,h),h}function d(e){for(let[t,r]of e)p(r)===i.PrefetchCacheEntryStatus.expired&&e.delete(t)}let f=1e3*Number("30"),h=1e3*Number("300");function p(e){let{kind:t,prefetchTime:r,lastUsedTime:n}=e;return Date.now()<(null!=n?n:r)+f?n?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.fresh:"auto"===t&&Date.now()<r+h?i.PrefetchCacheEntryStatus.stale:"full"===t&&Date.now()<r+h?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5703:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fastRefreshReducer",{enumerable:!0,get:function(){return n}}),r(9009),r(7584),r(5166),r(3772),r(941),r(7252),r(9894),r(2994),r(5652),r(5262);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2492:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return o}});let n=r(9886);function o(e,t){return function e(t,r,o){if(0===Object.keys(r).length)return[t,o];for(let i in r){let[a,s]=r[i],l=t.parallelRoutes.get(i);if(!l)continue;let u=(0,n.createRouterCacheKey)(a),c=l.get(u);if(!c)continue;let d=e(c,s,o+"/"+u);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2162:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e[1]:e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentValue",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5262:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasInterceptionRouteInCurrentTree",{enumerable:!0,get:function(){return function e(t){let[r,o]=t;if(Array.isArray(r)&&("di"===r[2]||"ci"===r[2])||"string"==typeof r&&(0,n.isInterceptionRouteAppPath)(r))return!0;if(o){for(let t in o)if(e(o[t]))return!0}return!1}}});let n=r(7356);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},941:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return y},navigateReducer:function(){return v}}),r(9009);let n=r(7584),o=r(3193),i=r(5166),a=r(4614),s=r(3772),l=r(7767),u=r(7252),c=r(9894),d=r(1156),f=r(2994),h=r(8071),p=(r(8831),r(9373)),m=r(2895);function y(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,u.handleMutable)(e,t)}function g(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,o]of Object.entries(n))for(let n of g(o))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}let v=function(e,t){let{url:r,isExternalUrl:v,navigateType:b,shouldScroll:x}=t,P={},{hash:_}=r,w=(0,n.createHrefFromUrl)(r),j="push"===b;if((0,p.prunePrefetchCache)(e.prefetchCache),P.preserveCustomHistoryState=!1,v)return y(e,P,r.toString(),j);let R=(0,p.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,tree:e.tree,buildId:e.buildId,prefetchCache:e.prefetchCache}),{treeAtTimeOfPrefetch:S,data:E}=R;return d.prefetchQueue.bump(E),E.then(t=>{let[r,d]=t,p=!1;if(R.lastUsedTime||(R.lastUsedTime=Date.now(),p=!0),"string"==typeof r)return y(e,P,r,j);if(document.getElementById("__next-page-redirect"))return y(e,P,w,j);let v=e.tree,b=e.cache,E=[];for(let t of r){let r=t.slice(0,-4),n=t.slice(-3)[0],u=["",...r],d=(0,i.applyRouterStatePatchToTree)(u,v,n,w);if(null===d&&(d=(0,i.applyRouterStatePatchToTree)(u,S,n,w)),null!==d){if((0,s.isNavigatingToNewRootLayout)(v,d))return y(e,P,w,j);let i=(0,f.createEmptyCacheNode)(),x=!1;for(let e of(R.status!==l.PrefetchCacheEntryStatus.stale||p?x=(0,c.applyFlightData)(b,i,t,R):(x=function(e,t,r,n){let o=!1;for(let i of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),g(n).map(e=>[...r,...e])))(0,m.clearCacheNodeDataForSegmentPath)(e,t,i),o=!0;return o}(i,b,r,n),R.lastUsedTime=Date.now()),(0,a.shouldHardNavigate)(u,v)?(i.rsc=b.rsc,i.prefetchRsc=b.prefetchRsc,(0,o.invalidateCacheBelowFlightSegmentPath)(i,b,r),P.cache=i):x&&(P.cache=i,b=i),v=d,g(n))){let t=[...r,...e];t[t.length-1]!==h.DEFAULT_SEGMENT_KEY&&E.push(t)}}}return P.patchedTree=v,P.canonicalUrl=d?(0,n.createHrefFromUrl)(d):w,P.pendingPush=j,P.scrollableSegments=E,P.hashFragment=_,P.shouldScroll=x,(0,u.handleMutable)(e,P)},()=>e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1156:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return s}});let n=r(5138),o=r(7815),i=r(9373),a=new o.PromiseQueue(5);function s(e,t){(0,i.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return r.searchParams.delete(n.NEXT_RSC_UNION_QUERY),(0,i.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,buildId:e.buildId}),e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9809:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let n=r(9009),o=r(7584),i=r(5166),a=r(3772),s=r(941),l=r(7252),u=r(114),c=r(2994),d=r(5652),f=r(5262),h=r(4158);function p(e,t){let{origin:r}=t,p={},m=e.canonicalUrl,y=e.tree;p.preserveCustomHistoryState=!1;let g=(0,c.createEmptyCacheNode)(),v=(0,f.hasInterceptionRouteInCurrentTree)(e.tree);return g.lazyData=(0,n.fetchServerResponse)(new URL(m,r),[y[0],y[1],y[2],"refetch"],v?e.nextUrl:null,e.buildId),g.lazyData.then(async r=>{let[n,c]=r;if("string"==typeof n)return(0,s.handleExternalUrl)(e,p,n,e.pushRef.pendingPush);for(let r of(g.lazyData=null,n)){if(3!==r.length)return console.log("REFRESH FAILED"),e;let[n]=r,l=(0,i.applyRouterStatePatchToTree)([""],y,n,e.canonicalUrl);if(null===l)return(0,d.handleSegmentMismatch)(e,t,n);if((0,a.isNavigatingToNewRootLayout)(y,l))return(0,s.handleExternalUrl)(e,p,m,e.pushRef.pendingPush);let f=c?(0,o.createHrefFromUrl)(c):void 0;c&&(p.canonicalUrl=f);let[b,x]=r.slice(-2);if(null!==b){let e=b[2];g.rsc=e,g.prefetchRsc=null,(0,u.fillLazyItemsTillLeafWithHead)(g,void 0,n,b,x),p.prefetchCache=new Map}await (0,h.refreshInactiveParallelSegments)({state:e,updatedTree:l,updatedCache:g,includeNextUrl:v,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=g,p.patchedTree=l,p.canonicalUrl=m,y=l}return(0,l.handleMutable)(e,p)},()=>e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5608:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return i}});let n=r(7584),o=r(3648);function i(e,t){var r;let{url:i,tree:a}=t,s=(0,n.createHrefFromUrl)(i),l=a||e.tree,u=e.cache;return{buildId:e.buildId,canonicalUrl:s,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(r=(0,o.extractPathFromFlightRouterState)(l))?r:i.pathname}}r(8831),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5240:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return b}});let n=r(5424),o=r(5138),i=r(3486),a=r(7584),s=r(941),l=r(5166),u=r(3772),c=r(7252),d=r(114),f=r(2994),h=r(5262),p=r(5652),m=r(4158),{createFromFetch:y,encodeReply:g}=r(6493);async function v(e,t,r){let a,{actionId:s,actionArgs:l}=r,u=await g(l),c=await fetch("",{method:"POST",headers:{Accept:o.RSC_CONTENT_TYPE_HEADER,[o.ACTION]:s,[o.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(e.tree)),...t?{[o.NEXT_URL]:t}:{}},body:u}),d=c.headers.get("x-action-redirect");try{let e=JSON.parse(c.headers.get("x-action-revalidated")||"[[],0,0]");a={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){a={paths:[],tag:!1,cookie:!1}}let f=d?new URL((0,i.addBasePath)(d),new URL(e.canonicalUrl,window.location.href)):void 0;if(c.headers.get("content-type")===o.RSC_CONTENT_TYPE_HEADER){let e=await y(Promise.resolve(c),{callServer:n.callServer});if(d){let[,t]=null!=e?e:[];return{actionFlightData:t,redirectLocation:f,revalidatedParts:a}}let[t,[,r]]=null!=e?e:[];return{actionResult:t,actionFlightData:r,redirectLocation:f,revalidatedParts:a}}return{redirectLocation:f,revalidatedParts:a}}function b(e,t){let{resolve:r,reject:n}=t,o={},i=e.canonicalUrl,y=e.tree;o.preserveCustomHistoryState=!1;let g=e.nextUrl&&(0,h.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null;return o.inFlightServerAction=v(e,g,t),o.inFlightServerAction.then(async n=>{let{actionResult:h,actionFlightData:v,redirectLocation:b}=n;if(b&&(e.pushRef.pendingPush=!0,o.pendingPush=!0),!v)return(r(h),b)?(0,s.handleExternalUrl)(e,o,b.href,e.pushRef.pendingPush):e;if("string"==typeof v)return(0,s.handleExternalUrl)(e,o,v,e.pushRef.pendingPush);if(o.inFlightServerAction=null,b){let e=(0,a.createHrefFromUrl)(b,!1);o.canonicalUrl=e}for(let r of v){if(3!==r.length)return console.log("SERVER ACTION APPLY FAILED"),e;let[n]=r,c=(0,l.applyRouterStatePatchToTree)([""],y,n,b?(0,a.createHrefFromUrl)(b):e.canonicalUrl);if(null===c)return(0,p.handleSegmentMismatch)(e,t,n);if((0,u.isNavigatingToNewRootLayout)(y,c))return(0,s.handleExternalUrl)(e,o,i,e.pushRef.pendingPush);let[h,v]=r.slice(-2),x=null!==h?h[2]:null;if(null!==x){let t=(0,f.createEmptyCacheNode)();t.rsc=x,t.prefetchRsc=null,(0,d.fillLazyItemsTillLeafWithHead)(t,void 0,n,h,v),await (0,m.refreshInactiveParallelSegments)({state:e,updatedTree:c,updatedCache:t,includeNextUrl:!!g,canonicalUrl:o.canonicalUrl||e.canonicalUrl}),o.cache=t,o.prefetchCache=new Map}o.patchedTree=c,y=c}return r(h),(0,c.handleMutable)(e,o)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4025:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return d}});let n=r(7584),o=r(5166),i=r(3772),a=r(941),s=r(9894),l=r(7252),u=r(2994),c=r(5652);function d(e,t){let{serverResponse:r}=t,[d,f]=r,h={};if(h.preserveCustomHistoryState=!1,"string"==typeof d)return(0,a.handleExternalUrl)(e,h,d,e.pushRef.pendingPush);let p=e.tree,m=e.cache;for(let r of d){let l=r.slice(0,-4),[d]=r.slice(-3,-2),y=(0,o.applyRouterStatePatchToTree)(["",...l],p,d,e.canonicalUrl);if(null===y)return(0,c.handleSegmentMismatch)(e,t,d);if((0,i.isNavigatingToNewRootLayout)(p,y))return(0,a.handleExternalUrl)(e,h,e.canonicalUrl,e.pushRef.pendingPush);let g=f?(0,n.createHrefFromUrl)(f):void 0;g&&(h.canonicalUrl=g);let v=(0,u.createEmptyCacheNode)();(0,s.applyFlightData)(m,v,r),h.patchedTree=y,h.cache=v,m=v,p=y}return(0,l.handleMutable)(e,h)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4158:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,o,,a]=t;for(let s in n.includes(i.PAGE_SEGMENT_KEY)&&"refresh"!==a&&(t[2]=r,t[3]="refresh"),o)e(o[s],r)}},refreshInactiveParallelSegments:function(){return a}});let n=r(9894),o=r(9009),i=r(8071);async function a(e){let t=new Set;await s({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function s(e){let{state:t,updatedTree:r,updatedCache:i,includeNextUrl:a,fetchedSegments:l,rootTree:u=r,canonicalUrl:c}=e,[,d,f,h]=r,p=[];if(f&&f!==c&&"refresh"===h&&!l.has(f)){l.add(f);let e=(0,o.fetchServerResponse)(new URL(f,location.origin),[u[0],u[1],u[2],"refetch"],a?t.nextUrl:null,t.buildId).then(e=>{let t=e[0];if("string"!=typeof t)for(let e of t)(0,n.applyFlightData)(i,i,e)});p.push(e)}for(let e in d){let r=s({state:t,updatedTree:d[e],updatedCache:i,includeNextUrl:a,fetchedSegments:l,rootTree:u,canonicalUrl:c});p.push(r)}await Promise.all(p)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7767:(e,t)=>{"use strict";var r,n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_FAST_REFRESH:function(){return u},ACTION_NAVIGATE:function(){return i},ACTION_PREFETCH:function(){return l},ACTION_REFRESH:function(){return o},ACTION_RESTORE:function(){return a},ACTION_SERVER_ACTION:function(){return c},ACTION_SERVER_PATCH:function(){return s},PrefetchCacheEntryStatus:function(){return n},PrefetchKind:function(){return r},isThenable:function(){return d}});let o="refresh",i="navigate",a="restore",s="server-patch",l="prefetch",u="fast-refresh",c="server-action";function d(e){return e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then}(function(e){e.AUTO="auto",e.FULL="full",e.TEMPORARY="temporary"})(r||(r={})),function(e){e.fresh="fresh",e.reusable="reusable",e.expired="expired",e.stale="stale"}(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3860:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(7767),r(941),r(4025),r(5608),r(9809),r(1156),r(5703),r(5240);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4614:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[o,i]=r,[a,s]=t;return(0,n.matchSegment)(a,o)?!(t.length<=2)&&e(t.slice(2),i[s]):!!Array.isArray(a)}}});let n=r(455);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3325:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createDynamicallyTrackedSearchParams:function(){return s},createUntrackedSearchParams:function(){return a}});let n=r(5869),o=r(2846),i=r(2255);function a(e){let t=n.staticGenerationAsyncStorage.getStore();return t&&t.forceStatic?{}:e}function s(e){let t=n.staticGenerationAsyncStorage.getStore();return t?t.forceStatic?{}:t.isStaticGeneration||t.dynamicShouldError?new Proxy({},{get:(e,r,n)=>("string"==typeof r&&(0,o.trackDynamicDataAccessed)(t,"searchParams."+r),i.ReflectAdapter.get(e,r,n)),has:(e,r)=>("string"==typeof r&&(0,o.trackDynamicDataAccessed)(t,"searchParams."+r),Reflect.has(e,r)),ownKeys:e=>((0,o.trackDynamicDataAccessed)(t,"searchParams"),Reflect.ownKeys(e))}):e:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6488:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{StaticGenBailoutError:function(){return n},isStaticGenBailoutError:function(){return o}});let r="NEXT_STATIC_GEN_BAILOUT";class n extends Error{constructor(...e){super(...e),this.code=r}}function o(e){return"object"==typeof e&&null!==e&&"code"in e&&e.code===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9519:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unresolvedThenable",{enumerable:!0,get:function(){return r}});let r={then:()=>{}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7326:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{useReducerWithReduxDevtools:function(){return s},useUnwrapState:function(){return a}});let n=r(8374)._(r(7577)),o=r(7767);function i(e){if(e instanceof Map){let t={};for(let[r,n]of e.entries()){if("function"==typeof n){t[r]="fn()";continue}if("object"==typeof n&&null!==n){if(n.$$typeof){t[r]=n.$$typeof.toString();continue}if(n._bundlerConfig){t[r]="FlightData";continue}}t[r]=i(n)}return t}if("object"==typeof e&&null!==e){let t={};for(let r in e){let n=e[r];if("function"==typeof n){t[r]="fn()";continue}if("object"==typeof n&&null!==n){if(n.$$typeof){t[r]=n.$$typeof.toString();continue}if(n.hasOwnProperty("_bundlerConfig")){t[r]="FlightData";continue}}t[r]=i(n)}return t}return Array.isArray(e)?e.map(i):e}function a(e){return(0,o.isThenable)(e)?(0,n.use)(e):e}r(3879);let s=function(e){return[e,()=>{},()=>{}]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7929:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return o}});let n=r(4655);function o(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return i}});let n=r(3236),o=r(3067),i=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:i}=(0,o.parsePath)(e);return/\.[^/]+\/?$/.test(t)?""+(0,n.removeTrailingSlash)(t)+r+i:t.endsWith("/")?""+t+r+i:t+"/"+r+i};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4237:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(7929),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6401:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPathname:function(){return n},isFullStringUrl:function(){return o},parseUrl:function(){return i}});let r="http://n";function n(e){return new URL(e,r).pathname}function o(e){return/https?:\/\//.test(e)}function i(e){let t;try{t=new URL(e,r)}catch{}return t}},2846:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Postpone:function(){return d},createPostponedAbortSignal:function(){return g},createPrerenderState:function(){return l},formatDynamicAPIAccesses:function(){return m},markCurrentScopeAsDynamic:function(){return u},trackDynamicDataAccessed:function(){return c},trackDynamicFetch:function(){return f},usedDynamicAPIs:function(){return p}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(7577)),o=r(442),i=r(6488),a=r(6401),s="function"==typeof n.default.unstable_postpone;function l(e){return{isDebugSkeleton:e,dynamicAccesses:[]}}function u(e,t){let r=(0,a.getPathname)(e.urlPathname);if(!e.isUnstableCacheCallback){if(e.dynamicShouldError)throw new i.StaticGenBailoutError(`Route ${r} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(e.prerenderState)h(e.prerenderState,t,r);else if(e.revalidate=0,e.isStaticGeneration){let n=new o.DynamicServerError(`Route ${r} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);throw e.dynamicUsageDescription=t,e.dynamicUsageStack=n.stack,n}}}function c(e,t){let r=(0,a.getPathname)(e.urlPathname);if(e.isUnstableCacheCallback)throw Error(`Route ${r} used "${t}" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "${t}" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`);if(e.dynamicShouldError)throw new i.StaticGenBailoutError(`Route ${r} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(e.prerenderState)h(e.prerenderState,t,r);else if(e.revalidate=0,e.isStaticGeneration){let n=new o.DynamicServerError(`Route ${r} couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);throw e.dynamicUsageDescription=t,e.dynamicUsageStack=n.stack,n}}function d({reason:e,prerenderState:t,pathname:r}){h(t,e,r)}function f(e,t){e.prerenderState&&h(e.prerenderState,t,e.urlPathname)}function h(e,t,r){y();let o=`Route ${r} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`;e.dynamicAccesses.push({stack:e.isDebugSkeleton?Error().stack:void 0,expression:t}),n.default.unstable_postpone(o)}function p(e){return e.dynamicAccesses.length>0}function m(e){return e.dynamicAccesses.filter(e=>"string"==typeof e.stack&&e.stack.length>0).map(({expression:e,stack:t})=>(t=t.split("\n").slice(4).filter(e=>!(e.includes("node_modules/next/")||e.includes(" (<anonymous>)")||e.includes(" (node:"))).join("\n"),`Dynamic API Usage Debug - ${e}:
${t}`))}function y(){if(!s)throw Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js")}function g(e){y();let t=new AbortController;try{n.default.unstable_postpone(e)}catch(e){t.abort(e)}return t.signal}},2357:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentParam",{enumerable:!0,get:function(){return o}});let n=r(7356);function o(e){let t=n.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t));return(t&&(e=e.slice(t.length)),e.startsWith("[[...")&&e.endsWith("]]"))?{type:"optional-catchall",param:e.slice(5,-2)}:e.startsWith("[...")&&e.endsWith("]")?{type:t?"catchall-intercepted":"catchall",param:e.slice(4,-1)}:e.startsWith("[")&&e.endsWith("]")?{type:t?"dynamic-intercepted":"dynamic",param:e.slice(1,-1)}:null}},7356:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return o},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return i}});let n=r(2862),o=["(..)(..)","(.)","(..)","(...)"];function i(e){return void 0!==e.split("/").find(e=>o.find(t=>e.startsWith(t)))}function a(e){let t,r,i;for(let n of e.split("/"))if(r=o.find(e=>n.startsWith(e))){[t,i]=e.split(r,2);break}if(!t||!r||!i)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":i="/"===t?`/${i}`:t+"/"+i;break;case"(..)":if("/"===t)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);i=t.split("/").slice(0,-1).concat(i).join("/");break;case"(...)":i="/"+i;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);i=a.slice(0,-2).concat(i).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:t,interceptedRoute:i}}},1616:(e,t,r)=>{"use strict";e.exports=r(399)},2413:(e,t,r)=>{"use strict";e.exports=r(1616).vendored.contexts.AppRouterContext},7008:(e,t,r)=>{"use strict";e.exports=r(1616).vendored.contexts.HooksClientContext},3347:(e,t,r)=>{"use strict";e.exports=r(1616).vendored.contexts.ServerInsertedHtml},962:(e,t,r)=>{"use strict";e.exports=r(1616).vendored["react-ssr"].ReactDOM},326:(e,t,r)=>{"use strict";e.exports=r(1616).vendored["react-ssr"].ReactJsxRuntime},6493:(e,t,r)=>{"use strict";e.exports=r(1616).vendored["react-ssr"].ReactServerDOMWebpackClientEdge},7577:(e,t,r)=>{"use strict";e.exports=r(1616).vendored["react-ssr"].React},2255:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},2165:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)&4294967295;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},4129:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return o}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},6058:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},3879:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ActionQueueContext:function(){return s},createMutableActionQueue:function(){return c}});let n=r(8374),o=r(7767),i=r(3860),a=n._(r(7577)),s=a.default.createContext(null);function l(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?u({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:o.ACTION_REFRESH,origin:window.location.origin},t)))}async function u(e){let{actionQueue:t,action:r,setState:n}=e,i=t.state;if(!i)throw Error("Invariant: Router state not initialized");t.pending=r;let a=r.payload,s=t.action(i,a);function u(e){r.discarded||(t.state=e,t.devToolsInstance&&t.devToolsInstance.send(a,e),l(t,n),r.resolve(e))}(0,o.isThenable)(s)?s.then(u,e=>{l(t,n),r.reject(e)}):u(s)}function c(){let e={state:null,dispatch:(t,r)=>(function(e,t,r){let n={resolve:r,reject:()=>{}};if(t.type!==o.ACTION_RESTORE){let e=new Promise((e,t)=>{n={resolve:e,reject:t}});(0,a.startTransition)(()=>{r(e)})}let i={payload:t,next:null,resolve:n.resolve,reject:n.reject};null===e.pending?(e.last=i,u({actionQueue:e,action:i,setState:r})):t.type===o.ACTION_NAVIGATE||t.type===o.ACTION_RESTORE?(e.pending.discarded=!0,e.last=i,e.pending.payload.type===o.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),u({actionQueue:e,action:i,setState:r})):(null!==e.last&&(e.last.next=i),e.last=i)})(e,t,r),action:async(e,t)=>{if(null===e)throw Error("Invariant: Router state not initialized");return(0,i.reducer)(e,t)},pending:null,last:null};return e}},8974:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return o}});let n=r(3067);function o(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:o,hash:i}=(0,n.parsePath)(e);return""+t+r+o+i}},2862:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return i},normalizeRscURL:function(){return a}});let n=r(6058),o=r(8071);function i(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,o.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},9976:(e,t)=>{"use strict";function r(e,t){if(void 0===t&&(t={}),t.onlyHashChange){e();return}let r=document.documentElement,n=r.style.scrollBehavior;r.style.scrollBehavior="auto",t.dontForceLayout||r.getClientRects(),e(),r.style.scrollBehavior=n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSmoothScroll",{enumerable:!0,get:function(){return r}})},2148:(e,t)=>{"use strict";function r(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isBot",{enumerable:!0,get:function(){return r}})},3067:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},4655:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return o}});let n=r(3067);function o(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},3236:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},8071:(e,t)=>{"use strict";function r(e){return"("===e[0]&&e.endsWith(")")}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_SEGMENT_KEY:function(){return o},PAGE_SEGMENT_KEY:function(){return n},isGroupSegment:function(){return r}});let n="__PAGE__",o="__DEFAULT__"},576:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},8570:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createProxy",{enumerable:!0,get:function(){return n}});let n=r(1749).createClientModuleProxy},9943:(e,t,r)=>{"use strict";let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/elgAto/emanuel/emanuel-ibarra-final-theme/app/node_modules/next/dist/client/components/app-router.js")},3144:(e,t,r)=>{"use strict";let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/elgAto/emanuel/emanuel-ibarra-final-theme/app/node_modules/next/dist/client/components/client-page.js")},7922:(e,t,r)=>{"use strict";let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/elgAto/emanuel/emanuel-ibarra-final-theme/app/node_modules/next/dist/client/components/error-boundary.js")},5106:(e,t,r)=>{"use strict";let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/elgAto/emanuel/emanuel-ibarra-final-theme/app/node_modules/next/dist/client/components/layout-router.js")},525:(e,t,r)=>{"use strict";let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/elgAto/emanuel/emanuel-ibarra-final-theme/app/node_modules/next/dist/client/components/not-found-boundary.js")},5866:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),r(3370);let n=r(9510);r(1159);let o={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{display:"inline-block"},h1:{display:"inline-block",margin:"0 20px 0 0",padding:"0 23px 0 0",fontSize:24,fontWeight:500,verticalAlign:"top",lineHeight:"49px"},h2:{fontSize:14,fontWeight:400,lineHeight:"49px",margin:0}};function i(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("title",{children:"404: This page could not be found."}),(0,n.jsx)("div",{style:o.error,children:(0,n.jsxs)("div",{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}),(0,n.jsx)("h1",{className:"next-error-h1",style:o.h1,children:"404"}),(0,n.jsx)("div",{style:o.desc,children:(0,n.jsx)("h2",{style:o.h2,children:"This page could not be found."})})]})})]})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4892:(e,t,r)=>{"use strict";let{createProxy:n}=r(8570);e.exports=n("/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/elgAto/emanuel/emanuel-ibarra-final-theme/app/node_modules/next/dist/client/components/render-from-template-context.js")},9181:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createDynamicallyTrackedSearchParams:function(){return s},createUntrackedSearchParams:function(){return a}});let n=r(5869),o=r(6278),i=r(8238);function a(e){let t=n.staticGenerationAsyncStorage.getStore();return t&&t.forceStatic?{}:e}function s(e){let t=n.staticGenerationAsyncStorage.getStore();return t?t.forceStatic?{}:t.isStaticGeneration||t.dynamicShouldError?new Proxy({},{get:(e,r,n)=>("string"==typeof r&&(0,o.trackDynamicDataAccessed)(t,"searchParams."+r),i.ReflectAdapter.get(e,r,n)),has:(e,r)=>("string"==typeof r&&(0,o.trackDynamicDataAccessed)(t,"searchParams."+r),Reflect.has(e,r)),ownKeys:e=>((0,o.trackDynamicDataAccessed)(t,"searchParams"),Reflect.ownKeys(e))}):e:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5231:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{AppRouter:function(){return o.default},ClientPageRoot:function(){return c.ClientPageRoot},LayoutRouter:function(){return i.default},NotFoundBoundary:function(){return h.NotFoundBoundary},Postpone:function(){return y.Postpone},RenderFromTemplateContext:function(){return a.default},actionAsyncStorage:function(){return u.actionAsyncStorage},createDynamicallyTrackedSearchParams:function(){return d.createDynamicallyTrackedSearchParams},createUntrackedSearchParams:function(){return d.createUntrackedSearchParams},decodeAction:function(){return n.decodeAction},decodeFormState:function(){return n.decodeFormState},decodeReply:function(){return n.decodeReply},patchFetch:function(){return x},preconnect:function(){return m.preconnect},preloadFont:function(){return m.preloadFont},preloadStyle:function(){return m.preloadStyle},renderToReadableStream:function(){return n.renderToReadableStream},requestAsyncStorage:function(){return l.requestAsyncStorage},serverHooks:function(){return f},staticGenerationAsyncStorage:function(){return s.staticGenerationAsyncStorage},taintObjectReference:function(){return g.taintObjectReference}});let n=r(1749),o=v(r(9943)),i=v(r(5106)),a=v(r(4892)),s=r(5869),l=r(4580),u=r(2934),c=r(3144),d=r(9181),f=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=b(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var a=o?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(n,i,a):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(4789)),h=r(525),p=r(670);r(7922);let m=r(135),y=r(9257),g=r(526);function v(e){return e&&e.__esModule?e:{default:e}}function b(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(b=function(e){return e?r:t})(e)}function x(){return(0,p.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:s.staticGenerationAsyncStorage})}},9257:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Postpone",{enumerable:!0,get:function(){return n.Postpone}});let n=r(6278)},135:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{preconnect:function(){return a},preloadFont:function(){return i},preloadStyle:function(){return o}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(7049));function o(e,t){let r={as:"style"};"string"==typeof t&&(r.crossOrigin=t),n.default.preload(e,r)}function i(e,t,r){let o={as:"font",type:t};"string"==typeof r&&(o.crossOrigin=r),n.default.preload(e,o)}function a(e,t){n.default.preconnect(e,"string"==typeof t?{crossOrigin:t}:void 0)}},526:(e,t,r)=>{"use strict";function n(){throw Error("Taint can only be used with the taint flag.")}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{taintObjectReference:function(){return o},taintUniqueValue:function(){return i}}),r(1159);let o=n,i=n},7049:(e,t,r)=>{"use strict";e.exports=r(3191).vendored["react-rsc"].ReactDOM},9510:(e,t,r)=>{"use strict";e.exports=r(3191).vendored["react-rsc"].ReactJsxRuntime},1749:(e,t,r)=>{"use strict";e.exports=r(3191).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},8051:(e,t,r)=>{"use strict";r.d(t,{F:()=>o,e:()=>i});var n=r(7577);function o(...e){return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}function i(...e){return n.useCallback(o(...e),e)}},4214:(e,t,r)=>{"use strict";r.d(t,{g7:()=>a});var n=r(7577),o=r(8051),i=r(326),a=n.forwardRef((e,t)=>{let{children:r,...o}=e,a=n.Children.toArray(r),l=a.find(u);if(l){let e=l.props.children,r=a.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(s,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,i.jsx)(s,{...o,ref:t,children:r})});a.displayName="Slot";var s=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return n.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{i(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props),ref:t?(0,o.F)(t,e):e})}return n.Children.count(r)>1?n.Children.only(null):null});s.displayName="SlotClone";var l=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});function u(e){return n.isValidElement(e)&&e.type===l}},8285:(e,t,r)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n,_class_private_field_loose_base:()=>n})},8817:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>o,_class_private_field_loose_key:()=>o});var n=0;function o(e){return"__private_"+n+++"_"+e}},1174:(e,t,r)=>{"use strict";function n(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>n,_interop_require_default:()=>n})},8374:(e,t,r)=>{"use strict";function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}function o(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=i?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(o,a,s):o[a]=e[a]}return o.default=e,r&&r.set(e,o),o}r.r(t),r.d(t,{_:()=>o,_interop_require_wildcard:()=>o})},8671:(e,t,r)=>{"use strict";r.d(t,{j:()=>i});let n=e=>"boolean"==typeof e?"".concat(e):0===e?"0":e,o=function(){for(var e,t,r=0,n="";r<arguments.length;)(e=arguments[r++])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t))for(r=0;r<t.length;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n);else for(r in t)t[r]&&(o&&(o+=" "),o+=r)}return o}(e))&&(n&&(n+=" "),n+=t);return n},i=(e,t)=>r=>{var i;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:s}=t,l=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],o=null==s?void 0:s[e];if(null===t)return null;let i=n(t)||n(o);return a[e][i]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,l,null==t?void 0:null===(i=t.compoundVariants)||void 0===i?void 0:i.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...u}[t]):({...s,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},1135:(e,t,r)=>{"use strict";function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n)}return o}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{W:()=>n,Z:()=>o});let o=n},339:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});let n=(0,r(7577).createContext)({})},295:(e,t,r)=>{"use strict";r.d(t,{O:()=>n});let n=(0,r(7577).createContext)(null)},805:(e,t,r)=>{"use strict";r.d(t,{Pn:()=>s,Wi:()=>a,frameData:()=>l,S6:()=>u});var n=r(4380);class o{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){let t=this.order.indexOf(e);-1!==t&&(this.order.splice(t,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}let i=["prepare","read","update","preRender","render","postRender"],{schedule:a,cancel:s,state:l,steps:u}=function(e,t){let r=!1,n=!0,a={delta:0,timestamp:0,isProcessing:!1},s=i.reduce((e,t)=>(e[t]=function(e){let t=new o,r=new o,n=0,i=!1,a=!1,s=new WeakSet,l={schedule:(e,o=!1,a=!1)=>{let l=a&&i,u=l?t:r;return o&&s.add(e),u.add(e)&&l&&i&&(n=t.order.length),e},cancel:e=>{r.remove(e),s.delete(e)},process:o=>{if(i){a=!0;return}if(i=!0,[t,r]=[r,t],r.clear(),n=t.order.length)for(let r=0;r<n;r++){let n=t.order[r];n(o),s.has(n)&&(l.schedule(n),e())}i=!1,a&&(a=!1,l.process(o))}};return l}(()=>r=!0),e),{}),l=e=>s[e].process(a),u=()=>{let o=performance.now();r=!1,a.delta=n?1e3/60:Math.max(Math.min(o-a.timestamp,40),1),a.timestamp=o,a.isProcessing=!0,i.forEach(l),a.isProcessing=!1,r&&t&&(n=!1,e(u))},c=()=>{r=!0,n=!0,a.isProcessing||e(u)};return{schedule:i.reduce((e,t)=>{let n=s[t];return e[t]=(e,t=!1,o=!1)=>(r||c(),n.schedule(e,t,o)),e},{}),cancel:e=>i.forEach(t=>s[t].cancel(e)),state:a,steps:s}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.Z,!0)},6856:(e,t,r)=>{"use strict";r.d(t,{E:()=>oI});var n=r(7577);let o=(0,n.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),i=(0,n.createContext)({});var a=r(295),s=r(2482);let l=(0,n.createContext)({strict:!1}),u=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),c="data-"+u("framerAppearId");function d(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function f(e){return"string"==typeof e||Array.isArray(e)}function h(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}let p=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],m=["initial",...p];function y(e){return h(e.animate)||m.some(t=>f(e[t]))}function g(e){return!!(y(e)||e.variants)}function v(e){return Array.isArray(e)?e.join(" "):e}let b={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},x={};for(let e in b)x[e]={isEnabled:t=>b[e].some(e=>!!t[e])};var P=r(8263),_=r(339);let w=(0,n.createContext)({}),j=Symbol.for("motionComponentSymbol"),R=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function S(e){if("string"!=typeof e||e.includes("-"));else if(R.indexOf(e)>-1||/[A-Z]/.test(e))return!0;return!1}let E={},T=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],O=new Set(T);function M(e,{layout:t,layoutId:r}){return O.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!E[e]||"opacity"===e)}let A=e=>!!(e&&e.getVelocity),C={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},D=T.length,k=e=>t=>"string"==typeof t&&t.startsWith(e),N=k("--"),L=k("var(--"),F=(e,t)=>t&&"number"==typeof e?t.transform(e):e,U=(e,t,r)=>Math.min(Math.max(r,e),t),V={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},I={...V,transform:e=>U(0,1,e)},B={...V,default:1},z=e=>Math.round(1e5*e)/1e5,H=/(-)?([\d]*\.?[\d])+/g,W=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,$=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function G(e){return"string"==typeof e}let K=e=>({test:t=>G(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),Y=K("deg"),X=K("%"),Z=K("px"),q=K("vh"),J=K("vw"),Q={...X,parse:e=>X.parse(e)/100,transform:e=>X.transform(100*e)},ee={...V,transform:Math.round},et={borderWidth:Z,borderTopWidth:Z,borderRightWidth:Z,borderBottomWidth:Z,borderLeftWidth:Z,borderRadius:Z,radius:Z,borderTopLeftRadius:Z,borderTopRightRadius:Z,borderBottomRightRadius:Z,borderBottomLeftRadius:Z,width:Z,maxWidth:Z,height:Z,maxHeight:Z,size:Z,top:Z,right:Z,bottom:Z,left:Z,padding:Z,paddingTop:Z,paddingRight:Z,paddingBottom:Z,paddingLeft:Z,margin:Z,marginTop:Z,marginRight:Z,marginBottom:Z,marginLeft:Z,rotate:Y,rotateX:Y,rotateY:Y,rotateZ:Y,scale:B,scaleX:B,scaleY:B,scaleZ:B,skew:Y,skewX:Y,skewY:Y,distance:Z,translateX:Z,translateY:Z,translateZ:Z,x:Z,y:Z,z:Z,perspective:Z,transformPerspective:Z,opacity:I,originX:Q,originY:Q,originZ:Z,zIndex:ee,fillOpacity:I,strokeOpacity:I,numOctaves:ee};function er(e,t,r,n){let{style:o,vars:i,transform:a,transformOrigin:s}=e,l=!1,u=!1,c=!0;for(let e in t){let r=t[e];if(N(e)){i[e]=r;continue}let n=et[e],d=F(r,n);if(O.has(e)){if(l=!0,a[e]=d,!c)continue;r!==(n.default||0)&&(c=!1)}else e.startsWith("origin")?(u=!0,s[e]=d):o[e]=d}if(!t.transform&&(l||n?o.transform=function(e,{enableHardwareAcceleration:t=!0,allowTransformNone:r=!0},n,o){let i="";for(let t=0;t<D;t++){let r=T[t];if(void 0!==e[r]){let t=C[r]||r;i+=`${t}(${e[r]}) `}}return t&&!e.z&&(i+="translateZ(0)"),i=i.trim(),o?i=o(e,n?"":i):r&&n&&(i="none"),i}(e.transform,r,c,n):o.transform&&(o.transform="none")),u){let{originX:e="50%",originY:t="50%",originZ:r=0}=s;o.transformOrigin=`${e} ${t} ${r}`}}let en=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function eo(e,t,r){for(let n in t)A(t[n])||M(n,r)||(e[n]=t[n])}let ei=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ea(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||ei.has(e)}let es=e=>!ea(e);try{!function(e){e&&(es=t=>t.startsWith("on")?!ea(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}function el(e,t,r){return"string"==typeof e?e:Z.transform(t+r*e)}let eu={offset:"stroke-dashoffset",array:"stroke-dasharray"},ec={offset:"strokeDashoffset",array:"strokeDasharray"};function ed(e,{attrX:t,attrY:r,attrScale:n,originX:o,originY:i,pathLength:a,pathSpacing:s=1,pathOffset:l=0,...u},c,d,f){if(er(e,u,c,f),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:h,style:p,dimensions:m}=e;h.transform&&(m&&(p.transform=h.transform),delete h.transform),m&&(void 0!==o||void 0!==i||p.transform)&&(p.transformOrigin=function(e,t,r){let n=el(t,e.x,e.width),o=el(r,e.y,e.height);return`${n} ${o}`}(m,void 0!==o?o:.5,void 0!==i?i:.5)),void 0!==t&&(h.x=t),void 0!==r&&(h.y=r),void 0!==n&&(h.scale=n),void 0!==a&&function(e,t,r=1,n=0,o=!0){e.pathLength=1;let i=o?eu:ec;e[i.offset]=Z.transform(-n);let a=Z.transform(t),s=Z.transform(r);e[i.array]=`${a} ${s}`}(h,a,s,l,!1)}let ef=()=>({...en(),attrs:{}}),eh=e=>"string"==typeof e&&"svg"===e.toLowerCase();function ep(e,{style:t,vars:r},n,o){for(let i in Object.assign(e.style,t,o&&o.getProjectionStyles(n)),r)e.style.setProperty(i,r[i])}let em=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ey(e,t,r,n){for(let r in ep(e,t,void 0,n),t.attrs)e.setAttribute(em.has(r)?r:u(r),t.attrs[r])}function eg(e,t){let{style:r}=e,n={};for(let o in r)(A(r[o])||t.style&&A(t.style[o])||M(o,e))&&(n[o]=r[o]);return n}function ev(e,t){let r=eg(e,t);for(let n in e)(A(e[n])||A(t[n]))&&(r[-1!==T.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}function eb(e,t,r,n={},o={}){return"function"==typeof t&&(t=t(void 0!==r?r:e.custom,n,o)),"string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t&&(t=t(void 0!==r?r:e.custom,n,o)),t}var ex=r(4749);let eP=e=>Array.isArray(e),e_=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),ew=e=>eP(e)?e[e.length-1]||0:e;function ej(e){let t=A(e)?e.get():e;return e_(t)?t.toValue():t}let eR=e=>(t,r)=>{let o=(0,n.useContext)(i),s=(0,n.useContext)(a.O),l=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:r},n,o,i){let a={latestValues:function(e,t,r,n){let o={},i=n(e,{});for(let e in i)o[e]=ej(i[e]);let{initial:a,animate:s}=e,l=y(e),u=g(e);t&&u&&!l&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===s&&(s=t.animate));let c=!!r&&!1===r.initial,d=(c=c||!1===a)?s:a;return d&&"boolean"!=typeof d&&!h(d)&&(Array.isArray(d)?d:[d]).forEach(t=>{let r=eb(e,t);if(!r)return;let{transitionEnd:n,transition:i,...a}=r;for(let e in a){let t=a[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let e in n)o[e]=n[e]}),o}(n,o,i,e),renderState:t()};return r&&(a.mount=e=>r(n,e,a)),a})(e,t,o,s);return r?l():(0,ex.h)(l)};var eS=r(805);let eE={useVisualState:eR({scrapeMotionValuesFromProps:ev,createRenderState:ef,onMount:(e,t,{renderState:r,latestValues:n})=>{eS.Wi.read(()=>{try{r.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(e){r.dimensions={x:0,y:0,width:0,height:0}}}),eS.Wi.render(()=>{ed(r,n,{enableHardwareAcceleration:!1},eh(t.tagName),e.transformTemplate),ey(t,r)})}})},eT={useVisualState:eR({scrapeMotionValuesFromProps:eg,createRenderState:en})};function eO(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}let eM=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function eA(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}let eC=e=>t=>eM(t)&&e(t,eA(t));function eD(e,t,r,n){return eO(e,t,eC(r),n)}let ek=(e,t)=>r=>t(e(r)),eN=(...e)=>e.reduce(ek);function eL(e){let t=null;return()=>null===t&&(t=e,()=>{t=null})}let eF=eL("dragHorizontal"),eU=eL("dragVertical");function eV(e){let t=!1;if("y"===e)t=eU();else if("x"===e)t=eF();else{let e=eF(),r=eU();e&&r?t=()=>{e(),r()}:(e&&e(),r&&r())}return t}function eI(){let e=eV(!0);return!e||(e(),!1)}class eB{constructor(e){this.isMounted=!1,this.node=e}update(){}}function ez(e,t){let r="onHover"+(t?"Start":"End");return eD(e.current,"pointer"+(t?"enter":"leave"),(n,o)=>{if("touch"===n.pointerType||eI())return;let i=e.getProps();e.animationState&&i.whileHover&&e.animationState.setActive("whileHover",t),i[r]&&eS.Wi.update(()=>i[r](n,o))},{passive:!e.getProps()[r]})}class eH extends eB{mount(){this.unmount=eN(ez(this.node,!0),ez(this.node,!1))}unmount(){}}class eW extends eB{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eN(eO(this.node.current,"focus",()=>this.onFocus()),eO(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let e$=(e,t)=>!!t&&(e===t||e$(e,t.parentElement));var eG=r(4380);function eK(e,t){if(!t)return;let r=new PointerEvent("pointer"+e);t(r,eA(r))}class eY extends eB{constructor(){super(...arguments),this.removeStartListeners=eG.Z,this.removeEndListeners=eG.Z,this.removeAccessibleListeners=eG.Z,this.startPointerPress=(e,t)=>{if(this.isPressing)return;this.removeEndListeners();let r=this.node.getProps(),n=eD(window,"pointerup",(e,t)=>{if(!this.checkPressEnd())return;let{onTap:r,onTapCancel:n,globalTapTarget:o}=this.node.getProps();eS.Wi.update(()=>{o||e$(this.node.current,e.target)?r&&r(e,t):n&&n(e,t)})},{passive:!(r.onTap||r.onPointerUp)}),o=eD(window,"pointercancel",(e,t)=>this.cancelPress(e,t),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=eN(n,o),this.startPress(e,t)},this.startAccessiblePress=()=>{let e=eO(this.node.current,"keydown",e=>{"Enter"!==e.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=eO(this.node.current,"keyup",e=>{"Enter"===e.key&&this.checkPressEnd()&&eK("up",(e,t)=>{let{onTap:r}=this.node.getProps();r&&eS.Wi.update(()=>r(e,t))})}),eK("down",(e,t)=>{this.startPress(e,t)}))}),t=eO(this.node.current,"blur",()=>{this.isPressing&&eK("cancel",(e,t)=>this.cancelPress(e,t))});this.removeAccessibleListeners=eN(e,t)}}startPress(e,t){this.isPressing=!0;let{onTapStart:r,whileTap:n}=this.node.getProps();n&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&eS.Wi.update(()=>r(e,t))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!eI()}cancelPress(e,t){if(!this.checkPressEnd())return;let{onTapCancel:r}=this.node.getProps();r&&eS.Wi.update(()=>r(e,t))}mount(){let e=this.node.getProps(),t=eD(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),r=eO(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=eN(t,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let eX=new WeakMap,eZ=new WeakMap,eq=e=>{let t=eX.get(e.target);t&&t(e)},eJ=e=>{e.forEach(eq)},eQ={some:0,all:1};class e0 extends eB{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:o}=e,i={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:eQ[n]};return function(e,t,r){let n=function({root:e,...t}){let r=e||document;eZ.has(r)||eZ.set(r,{});let n=eZ.get(r),o=JSON.stringify(t);return n[o]||(n[o]=new IntersectionObserver(eJ,{root:e,...t})),n[o]}(t);return eX.set(e,r),n.observe(e),()=>{eX.delete(e),n.unobserve(e)}}(this.node.current,i,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,o&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),i=t?r:n;i&&i(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t))&&this.startObserver()}unmount(){}}function e1(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}function e2(e,t,r){let n=e.getProps();return eb(n,t,void 0!==r?r:n.custom,function(e){let t={};return e.values.forEach((e,r)=>t[r]=e.get()),t}(e),function(e){let t={};return e.values.forEach((e,r)=>t[r]=e.getVelocity()),t}(e))}var e5=r(4673);let e7=e=>1e3*e,e3=e=>e/1e3,e4={current:!1},e8=e=>Array.isArray(e)&&"number"==typeof e[0],e6=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,e9={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:e6([0,.65,.55,1]),circOut:e6([.55,0,1,.45]),backIn:e6([.31,.01,.66,-.59]),backOut:e6([.33,1.53,.69,.99])},te=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function tt(e,t,r,n){if(e===t&&r===n)return eG.Z;let o=t=>(function(e,t,r,n,o){let i,a;let s=0;do(i=te(a=t+(r-t)/2,n,o)-e)>0?r=a:t=a;while(Math.abs(i)>1e-7&&++s<12);return a})(t,0,1,e,r);return e=>0===e||1===e?e:te(o(e),t,n)}let tr=tt(.42,0,1,1),tn=tt(0,0,.58,1),to=tt(.42,0,.58,1),ti=e=>Array.isArray(e)&&"number"!=typeof e[0],ta=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,ts=e=>t=>1-e(1-t),tl=e=>1-Math.sin(Math.acos(e)),tu=ts(tl),tc=ta(tl),td=tt(.33,1.53,.69,.99),tf=ts(td),th=ta(tf),tp={linear:eG.Z,easeIn:tr,easeInOut:to,easeOut:tn,circIn:tl,circInOut:tc,circOut:tu,backIn:tf,backInOut:th,backOut:td,anticipate:e=>(e*=2)<1?.5*tf(e):.5*(2-Math.pow(2,-10*(e-1)))},tm=e=>{if(Array.isArray(e)){(0,e5.k)(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,r,n,o]=e;return tt(t,r,n,o)}return"string"==typeof e?((0,e5.k)(void 0!==tp[e],`Invalid easing type '${e}'`),tp[e]):e},ty=(e,t)=>r=>!!(G(r)&&$.test(r)&&r.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(r,t)),tg=(e,t,r)=>n=>{if(!G(n))return n;let[o,i,a,s]=n.match(H);return{[e]:parseFloat(o),[t]:parseFloat(i),[r]:parseFloat(a),alpha:void 0!==s?parseFloat(s):1}},tv=e=>U(0,255,e),tb={...V,transform:e=>Math.round(tv(e))},tx={test:ty("rgb","red"),parse:tg("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+tb.transform(e)+", "+tb.transform(t)+", "+tb.transform(r)+", "+z(I.transform(n))+")"},tP={test:ty("#"),parse:function(e){let t="",r="",n="",o="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),o=e.substring(4,5),t+=t,r+=r,n+=n,o+=o),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:o?parseInt(o,16)/255:1}},transform:tx.transform},t_={test:ty("hsl","hue"),parse:tg("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:n=1})=>"hsla("+Math.round(e)+", "+X.transform(z(t))+", "+X.transform(z(r))+", "+z(I.transform(n))+")"},tw={test:e=>tx.test(e)||tP.test(e)||t_.test(e),parse:e=>tx.test(e)?tx.parse(e):t_.test(e)?t_.parse(e):tP.parse(e),transform:e=>G(e)?e:e.hasOwnProperty("red")?tx.transform(e):t_.transform(e)},tj=(e,t,r)=>-r*e+r*t+e;function tR(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}let tS=(e,t,r)=>{let n=e*e;return Math.sqrt(Math.max(0,r*(t*t-n)+n))},tE=[tP,tx,t_],tT=e=>tE.find(t=>t.test(e));function tO(e){let t=tT(e);(0,e5.k)(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`);let r=t.parse(e);return t===t_&&(r=function({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,r/=100;let o=0,i=0,a=0;if(t/=100){let n=r<.5?r*(1+t):r+t-r*t,s=2*r-n;o=tR(s,n,e+1/3),i=tR(s,n,e),a=tR(s,n,e-1/3)}else o=i=a=r;return{red:Math.round(255*o),green:Math.round(255*i),blue:Math.round(255*a),alpha:n}}(r)),r}let tM=(e,t)=>{let r=tO(e),n=tO(t),o={...r};return e=>(o.red=tS(r.red,n.red,e),o.green=tS(r.green,n.green,e),o.blue=tS(r.blue,n.blue,e),o.alpha=tj(r.alpha,n.alpha,e),tx.transform(o))},tA={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:eG.Z},tC={regex:W,countKey:"Colors",token:"${c}",parse:tw.parse},tD={regex:H,countKey:"Numbers",token:"${n}",parse:V.parse};function tk(e,{regex:t,countKey:r,token:n,parse:o}){let i=e.tokenised.match(t);i&&(e["num"+r]=i.length,e.tokenised=e.tokenised.replace(t,n),e.values.push(...i.map(o)))}function tN(e){let t=e.toString(),r={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return r.value.includes("var(--")&&tk(r,tA),tk(r,tC),tk(r,tD),r}function tL(e){return tN(e).values}function tF(e){let{values:t,numColors:r,numVars:n,tokenised:o}=tN(e),i=t.length;return e=>{let t=o;for(let o=0;o<i;o++)t=o<n?t.replace(tA.token,e[o]):o<n+r?t.replace(tC.token,tw.transform(e[o])):t.replace(tD.token,z(e[o]));return t}}let tU=e=>"number"==typeof e?0:e,tV={test:function(e){var t,r;return isNaN(e)&&G(e)&&((null===(t=e.match(H))||void 0===t?void 0:t.length)||0)+((null===(r=e.match(W))||void 0===r?void 0:r.length)||0)>0},parse:tL,createTransformer:tF,getAnimatableNone:function(e){let t=tL(e);return tF(e)(t.map(tU))}},tI=(e,t)=>r=>`${r>0?t:e}`;function tB(e,t){return"number"==typeof e?r=>tj(e,t,r):tw.test(e)?tM(e,t):e.startsWith("var(")?tI(e,t):tW(e,t)}let tz=(e,t)=>{let r=[...e],n=r.length,o=e.map((e,r)=>tB(e,t[r]));return e=>{for(let t=0;t<n;t++)r[t]=o[t](e);return r}},tH=(e,t)=>{let r={...e,...t},n={};for(let o in r)void 0!==e[o]&&void 0!==t[o]&&(n[o]=tB(e[o],t[o]));return e=>{for(let t in n)r[t]=n[t](e);return r}},tW=(e,t)=>{let r=tV.createTransformer(t),n=tN(e),o=tN(t);return n.numVars===o.numVars&&n.numColors===o.numColors&&n.numNumbers>=o.numNumbers?eN(tz(n.values,o.values),r):((0,e5.K)(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tI(e,t))},t$=(e,t,r)=>{let n=t-e;return 0===n?1:(r-e)/n},tG=(e,t)=>r=>tj(e,t,r);function tK(e,t,{clamp:r=!0,ease:n,mixer:o}={}){let i=e.length;if((0,e5.k)(i===t.length,"Both input and output ranges must be the same length"),1===i)return()=>t[0];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,r){let n=[],o=r||function(e){if("number"==typeof e);else if("string"==typeof e)return tw.test(e)?tM:tW;else if(Array.isArray(e))return tz;else if("object"==typeof e)return tH;return tG}(e[0]),i=e.length-1;for(let r=0;r<i;r++){let i=o(e[r],e[r+1]);t&&(i=eN(Array.isArray(t)?t[r]||eG.Z:t,i)),n.push(i)}return n}(t,n,o),s=a.length,l=t=>{let r=0;if(s>1)for(;r<e.length-2&&!(t<e[r+1]);r++);let n=t$(e[r],e[r+1],t);return a[r](n)};return r?t=>l(U(e[0],e[i-1],t)):l}function tY({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){let o=ti(n)?n.map(tm):tm(n),i={done:!1,value:t[0]},a=tK((r&&r.length===t.length?r:function(e){let t=[0];return function(e,t){let r=e[e.length-1];for(let n=1;n<=t;n++){let o=t$(0,t,n);e.push(tj(r,1,o))}}(t,e.length-1),t}(t)).map(t=>t*e),t,{ease:Array.isArray(o)?o:t.map(()=>o||to).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(i.value=a(t),i.done=t>=e,i)}}function tX(e,t,r){var n,o;let i=Math.max(t-5,0);return n=r-e(i),(o=t-i)?1e3/o*n:0}function tZ(e,t){return e*Math.sqrt(1-t*t)}let tq=["duration","bounce"],tJ=["stiffness","damping","mass"];function tQ(e,t){return t.some(t=>void 0!==e[t])}function t0({keyframes:e,restDelta:t,restSpeed:r,...n}){let o;let i=e[0],a=e[e.length-1],s={done:!1,value:i},{stiffness:l,damping:u,mass:c,duration:d,velocity:f,isResolvedFromDuration:h}=function(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!tQ(e,tJ)&&tQ(e,tq)){let r=function({duration:e=800,bounce:t=.25,velocity:r=0,mass:n=1}){let o,i;(0,e5.K)(e<=e7(10),"Spring duration must be 10 seconds or less");let a=1-t;a=U(.05,1,a),e=U(.01,10,e3(e)),a<1?(o=t=>{let n=t*a,o=n*e;return .001-(n-r)/tZ(t,a)*Math.exp(-o)},i=t=>{let n=t*a*e,i=Math.pow(a,2)*Math.pow(t,2)*e,s=tZ(Math.pow(t,2),a);return(n*r+r-i)*Math.exp(-n)*(-o(t)+.001>0?-1:1)/s}):(o=t=>-.001+Math.exp(-t*e)*((t-r)*e+1),i=t=>e*e*(r-t)*Math.exp(-t*e));let s=function(e,t,r){let n=r;for(let r=1;r<12;r++)n-=e(n)/t(n);return n}(o,i,5/e);if(e=e7(e),isNaN(s))return{stiffness:100,damping:10,duration:e};{let t=Math.pow(s,2)*n;return{stiffness:t,damping:2*a*Math.sqrt(n*t),duration:e}}}(e);(t={...t,...r,mass:1}).isResolvedFromDuration=!0}return t}({...n,velocity:-e3(n.velocity||0)}),p=f||0,m=u/(2*Math.sqrt(l*c)),y=a-i,g=e3(Math.sqrt(l/c)),v=5>Math.abs(y);if(r||(r=v?.01:2),t||(t=v?.005:.5),m<1){let e=tZ(g,m);o=t=>a-Math.exp(-m*g*t)*((p+m*g*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===m)o=e=>a-Math.exp(-g*e)*(y+(p+g*y)*e);else{let e=g*Math.sqrt(m*m-1);o=t=>{let r=Math.exp(-m*g*t),n=Math.min(e*t,300);return a-r*((p+m*g*y)*Math.sinh(n)+e*y*Math.cosh(n))/e}}return{calculatedDuration:h&&d||null,next:e=>{let n=o(e);if(h)s.done=e>=d;else{let i=p;0!==e&&(i=m<1?tX(o,e,n):0);let l=Math.abs(i)<=r,u=Math.abs(a-n)<=t;s.done=l&&u}return s.value=s.done?a:n,s}}}function t1({keyframes:e,velocity:t=0,power:r=.8,timeConstant:n=325,bounceDamping:o=10,bounceStiffness:i=500,modifyTarget:a,min:s,max:l,restDelta:u=.5,restSpeed:c}){let d,f;let h=e[0],p={done:!1,value:h},m=e=>void 0!==s&&e<s||void 0!==l&&e>l,y=e=>void 0===s?l:void 0===l?s:Math.abs(s-e)<Math.abs(l-e)?s:l,g=r*t,v=h+g,b=void 0===a?v:a(v);b!==v&&(g=b-h);let x=e=>-g*Math.exp(-e/n),P=e=>b+x(e),_=e=>{let t=x(e),r=P(e);p.done=Math.abs(t)<=u,p.value=p.done?b:r},w=e=>{m(p.value)&&(d=e,f=t0({keyframes:[p.value,y(p.value)],velocity:tX(P,e,p.value),damping:o,stiffness:i,restDelta:u,restSpeed:c}))};return w(0),{calculatedDuration:null,next:e=>{let t=!1;return(f||void 0!==d||(t=!0,_(e),w(e)),void 0!==d&&e>d)?f.next(e-d):(t||_(e),p)}}}let t2=e=>{let t=({timestamp:t})=>e(t);return{start:()=>eS.Wi.update(t,!0),stop:()=>(0,eS.Pn)(t),now:()=>eS.frameData.isProcessing?eS.frameData.timestamp:performance.now()}};function t5(e){let t=0,r=e.next(t);for(;!r.done&&t<2e4;)t+=50,r=e.next(t);return t>=2e4?1/0:t}let t7={decay:t1,inertia:t1,tween:tY,keyframes:tY,spring:t0};function t3({autoplay:e=!0,delay:t=0,driver:r=t2,keyframes:n,type:o="keyframes",repeat:i=0,repeatDelay:a=0,repeatType:s="loop",onPlay:l,onStop:u,onComplete:c,onUpdate:d,...f}){let h,p,m,y,g,v=1,b=!1,x=()=>{p=new Promise(e=>{h=e})};x();let P=t7[o]||tY;P!==tY&&"number"!=typeof n[0]&&(y=tK([0,100],n,{clamp:!1}),n=[0,100]);let _=P({...f,keyframes:n});"mirror"===s&&(g=P({...f,keyframes:[...n].reverse(),velocity:-(f.velocity||0)}));let w="idle",j=null,R=null,S=null;null===_.calculatedDuration&&i&&(_.calculatedDuration=t5(_));let{calculatedDuration:E}=_,T=1/0,O=1/0;null!==E&&(O=(T=E+a)*(i+1)-a);let M=0,A=e=>{if(null===R)return;v>0&&(R=Math.min(R,e)),v<0&&(R=Math.min(e-O/v,R));let r=(M=null!==j?j:Math.round(e-R)*v)-t*(v>=0?1:-1),o=v>=0?r<0:r>O;M=Math.max(r,0),"finished"===w&&null===j&&(M=O);let l=M,u=_;if(i){let e=Math.min(M,O)/T,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,(t=Math.min(t,i+1))%2&&("reverse"===s?(r=1-r,a&&(r-=a/T)):"mirror"===s&&(u=g)),l=U(0,1,r)*T}let c=o?{done:!1,value:n[0]}:u.next(l);y&&(c.value=y(c.value));let{done:f}=c;o||null===E||(f=v>=0?M>=O:M<=0);let h=null===j&&("finished"===w||"running"===w&&f);return d&&d(c.value),h&&k(),c},C=()=>{m&&m.stop(),m=void 0},D=()=>{w="idle",C(),h(),x(),R=S=null},k=()=>{w="finished",c&&c(),C(),h()},N=()=>{if(b)return;m||(m=r(A));let e=m.now();l&&l(),null!==j?R=e-j:R&&"finished"!==w||(R=e),"finished"===w&&x(),S=R,j=null,w="running",m.start()};e&&N();let L={then:(e,t)=>p.then(e,t),get time(){return e3(M)},set time(newTime){M=newTime=e7(newTime),null===j&&m&&0!==v?R=m.now()-newTime/v:j=newTime},get duration(){return e3(null===_.calculatedDuration?t5(_):_.calculatedDuration)},get speed(){return v},set speed(newSpeed){if(newSpeed===v||!m)return;v=newSpeed,L.time=e3(M)},get state(){return w},play:N,pause:()=>{w="paused",j=M},stop:()=>{b=!0,"idle"!==w&&(w="idle",u&&u(),D())},cancel:()=>{null!==S&&A(S),D()},complete:()=>{w="finished"},sample:e=>(R=0,A(e))};return L}let t4=function(e){let t;return()=>(void 0===t&&(t=e()),t)}(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),t8=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),t6=(e,t)=>"spring"===t.type||"backgroundColor"===e||!function e(t){return!!(!t||"string"==typeof t&&e9[t]||e8(t)||Array.isArray(t)&&t.every(e))}(t.ease),t9={type:"spring",stiffness:500,damping:25,restSpeed:10},re=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),rt={type:"keyframes",duration:.8},rr={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rn=(e,{keyframes:t})=>t.length>2?rt:O.has(e)?e.startsWith("scale")?re(t[1]):t9:rr,ro=(e,t)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tV.test(t)||"0"===t)&&!t.startsWith("url(")),ri=new Set(["brightness","contrast","saturate","opacity"]);function ra(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=r.match(H)||[];if(!n)return e;let o=r.replace(n,""),i=ri.has(t)?1:0;return n!==r&&(i*=100),t+"("+i+o+")"}let rs=/([a-z-]*)\(.*?\)/g,rl={...tV,getAnimatableNone:e=>{let t=e.match(rs);return t?t.map(ra).join(" "):e}},ru={...et,color:tw,backgroundColor:tw,outlineColor:tw,fill:tw,stroke:tw,borderColor:tw,borderTopColor:tw,borderRightColor:tw,borderBottomColor:tw,borderLeftColor:tw,filter:rl,WebkitFilter:rl},rc=e=>ru[e];function rd(e,t){let r=rc(e);return r!==rl&&(r=tV),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let rf=e=>/^0[^.\s]+$/.test(e);function rh(e,t){return e[t]||e.default||e}let rp={skipAnimations:!1},rm=(e,t,r,n={})=>o=>{let i=rh(n,e)||{},a=i.delay||n.delay||0,{elapsed:s=0}=n;s-=e7(a);let l=function(e,t,r,n){let o,i;let a=ro(t,r);o=Array.isArray(r)?[...r]:[null,r];let s=void 0!==n.from?n.from:e.get(),l=[];for(let e=0;e<o.length;e++){var u;null===o[e]&&(o[e]=0===e?s:o[e-1]),("number"==typeof(u=o[e])?0===u:null!==u?"none"===u||"0"===u||rf(u):void 0)&&l.push(e),"string"==typeof o[e]&&"none"!==o[e]&&"0"!==o[e]&&(i=o[e])}if(a&&l.length&&i)for(let e=0;e<l.length;e++)o[l[e]]=rd(t,i);return o}(t,e,r,i),u=l[0],c=l[l.length-1],d=ro(e,u),f=ro(e,c);(0,e5.K)(d===f,`You are trying to animate ${e} from "${u}" to "${c}". ${u} is not an animatable value - to enable this animation set ${u} to a value animatable to ${c} via the \`style\` property.`);let h={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...i,delay:-s,onUpdate:e=>{t.set(e),i.onUpdate&&i.onUpdate(e)},onComplete:()=>{o(),i.onComplete&&i.onComplete()}};if(!function({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:o,repeat:i,repeatType:a,repeatDelay:s,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(i)&&(h={...h,...rn(e,h)}),h.duration&&(h.duration=e7(h.duration)),h.repeatDelay&&(h.repeatDelay=e7(h.repeatDelay)),!d||!f||e4.current||!1===i.type||rp.skipAnimations)return function({keyframes:e,delay:t,onUpdate:r,onComplete:n}){let o=()=>(r&&r(e[e.length-1]),n&&n(),{time:0,speed:1,duration:0,play:eG.Z,pause:eG.Z,stop:eG.Z,then:e=>(e(),Promise.resolve()),cancel:eG.Z,complete:eG.Z});return t?t3({keyframes:[0,1],duration:0,delay:t,onComplete:o}):o()}(e4.current?{...h,delay:0}:h);if(!n.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){let r=function(e,t,{onUpdate:r,onComplete:n,...o}){let i,a;if(!(t4()&&t8.has(t)&&!o.repeatDelay&&"mirror"!==o.repeatType&&0!==o.damping&&"inertia"!==o.type))return!1;let s=!1,l=!1,u=()=>{a=new Promise(e=>{i=e})};u();let{keyframes:c,duration:d=300,ease:f,times:h}=o;if(t6(t,o)){let e=t3({...o,repeat:0,delay:0}),t={done:!1,value:c[0]},r=[],n=0;for(;!t.done&&n<2e4;)t=e.sample(n),r.push(t.value),n+=10;h=void 0,c=r,d=n-10,f="linear"}let p=function(e,t,r,{delay:n=0,duration:o,repeat:i=0,repeatType:a="loop",ease:s,times:l}={}){let u={[t]:r};l&&(u.offset=l);let c=function e(t){if(t)return e8(t)?e6(t):Array.isArray(t)?t.map(e):e9[t]}(s);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:n,duration:o,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:i+1,direction:"reverse"===a?"alternate":"normal"})}(e.owner.current,t,c,{...o,duration:d,ease:f,times:h}),m=()=>{l=!1,p.cancel()},y=()=>{l=!0,eS.Wi.update(m),i(),u()};return p.onfinish=()=>{l||(e.set(function(e,{repeat:t,repeatType:r="loop"}){let n=t&&"loop"!==r&&t%2==1?0:e.length-1;return e[n]}(c,o)),n&&n(),y())},{then:(e,t)=>a.then(e,t),attachTimeline:e=>(p.timeline=e,p.onfinish=null,eG.Z),get time(){return e3(p.currentTime||0)},set time(newTime){p.currentTime=e7(newTime)},get speed(){return p.playbackRate},set speed(newSpeed){p.playbackRate=newSpeed},get duration(){return e3(d)},play:()=>{s||(p.play(),(0,eS.Pn)(m))},pause:()=>p.pause(),stop:()=>{if(s=!0,"idle"===p.playState)return;let{currentTime:t}=p;if(t){let r=t3({...o,autoplay:!1});e.setWithVelocity(r.sample(t-10).value,r.sample(t).value,10)}y()},complete:()=>{l||p.finish()},cancel:y}}(t,e,h);if(r)return r}return t3(h)};function ry(e){return!!(A(e)&&e.add)}let rg=e=>/^\-?\d*\.?\d+$/.test(e);function rv(e,t){-1===e.indexOf(t)&&e.push(t)}function rb(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}class rx{constructor(){this.subscriptions=[]}add(e){return rv(this.subscriptions,e),()=>rb(this.subscriptions,e)}notify(e,t,r){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](e,t,r);else for(let o=0;o<n;o++){let n=this.subscriptions[o];n&&n(e,t,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let rP=e=>!isNaN(parseFloat(e)),r_={current:void 0};class rw{constructor(e,t={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(e,t=!0)=>{this.prev=this.current,this.current=e;let{delta:r,timestamp:n}=eS.frameData;this.lastUpdated!==n&&(this.timeDelta=r,this.lastUpdated=n,eS.Wi.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>eS.Wi.postRender(this.velocityCheck),this.velocityCheck=({timestamp:e})=>{e!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=rP(this.current),this.owner=t.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new rx);let r=this.events[e].add(t);return"change"===e?()=>{r(),eS.Wi.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=e,this.timeDelta=r}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return r_.current&&r_.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e,t;return this.canTrackVelocity?(e=parseFloat(this.current)-parseFloat(this.prev),(t=this.timeDelta)?1e3/t*e:0):0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function rj(e,t){return new rw(e,t)}let rR=e=>t=>t.test(e),rS=[V,Z,X,Y,J,q,{test:e=>"auto"===e,parse:e=>e}],rE=e=>rS.find(rR(e)),rT=[...rS,tw,tV],rO=e=>rT.find(rR(e));function rM(e,t,{delay:r=0,transitionOverride:n,type:o}={}){let{transition:i=e.getDefaultTransition(),transitionEnd:a,...s}=e.makeTargetAnimatable(t),l=e.getValue("willChange");n&&(i=n);let u=[],d=o&&e.animationState&&e.animationState.getState()[o];for(let t in s){let n=e.getValue(t),o=s[t];if(!n||void 0===o||d&&function({protectedKeys:e,needsAnimating:t},r){let n=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,n}(d,t))continue;let a={delay:r,elapsed:0,...rh(i||{},t)};if(window.HandoffAppearAnimations){let r=e.getProps()[c];if(r){let e=window.HandoffAppearAnimations(r,t,n,eS.Wi);null!==e&&(a.elapsed=e,a.isHandoff=!0)}}let f=!a.isHandoff&&!function(e,t){let r=e.get();if(!Array.isArray(t))return r!==t;for(let e=0;e<t.length;e++)if(t[e]!==r)return!0}(n,o);if("spring"===a.type&&(n.getVelocity()||a.velocity)&&(f=!1),n.animation&&(f=!1),f)continue;n.start(rm(t,n,o,e.shouldReduceMotion&&O.has(t)?{type:!1}:a));let h=n.animation;ry(l)&&(l.add(t),h.then(()=>l.remove(t))),u.push(h)}return a&&Promise.all(u).then(()=>{a&&function(e,t){let r=e2(e,t),{transitionEnd:n={},transition:o={},...i}=r?e.makeTargetAnimatable(r,!1):{};for(let t in i={...i,...n}){let r=ew(i[t]);e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,rj(r))}}(e,a)}),u}function rA(e,t,r={}){let n=e2(e,t,r.custom),{transition:o=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(o=r.transitionOverride);let i=n?()=>Promise.all(rM(e,n,r)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:i=0,staggerChildren:a,staggerDirection:s}=o;return function(e,t,r=0,n=0,o=1,i){let a=[],s=(e.variantChildren.size-1)*n,l=1===o?(e=0)=>e*n:(e=0)=>s-e*n;return Array.from(e.variantChildren).sort(rC).forEach((e,n)=>{e.notify("AnimationStart",t),a.push(rA(e,t,{...i,delay:r+l(n)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,i+n,a,s,r)}:()=>Promise.resolve(),{when:s}=o;if(!s)return Promise.all([i(),a(r.delay)]);{let[e,t]="beforeChildren"===s?[i,a]:[a,i];return e().then(()=>t())}}function rC(e,t){return e.sortNodePosition(t)}let rD=[...p].reverse(),rk=p.length;function rN(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class rL extends eB{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t))n=Promise.all(t.map(t=>rA(e,t,r)));else if("string"==typeof t)n=rA(e,t,r);else{let o="function"==typeof t?e2(e,t,r.custom):t;n=Promise.all(rM(e,o,r))}return n.then(()=>e.notify("AnimationComplete",t))})(e,t,r))),r={animate:rN(!0),whileInView:rN(),whileHover:rN(),whileTap:rN(),whileDrag:rN(),whileFocus:rN(),exit:rN()},n=!0,o=(t,r)=>{let n=e2(e,r);if(n){let{transition:e,transitionEnd:r,...o}=n;t={...t,...o,...r}}return t};function i(i,a){let s=e.getProps(),l=e.getVariantContext(!0)||{},u=[],c=new Set,d={},p=1/0;for(let t=0;t<rk;t++){var m;let y=rD[t],g=r[y],v=void 0!==s[y]?s[y]:l[y],b=f(v),x=y===a?g.isActive:null;!1===x&&(p=t);let P=v===l[y]&&v!==s[y]&&b;if(P&&n&&e.manuallyAnimateOnMount&&(P=!1),g.protectedKeys={...d},!g.isActive&&null===x||!v&&!g.prevProp||h(v)||"boolean"==typeof v)continue;let _=(m=g.prevProp,("string"==typeof v?v!==m:!!Array.isArray(v)&&!e1(v,m))||y===a&&g.isActive&&!P&&b||t>p&&b),w=!1,j=Array.isArray(v)?v:[v],R=j.reduce(o,{});!1===x&&(R={});let{prevResolvedValues:S={}}=g,E={...S,...R},T=e=>{_=!0,c.has(e)&&(w=!0,c.delete(e)),g.needsAnimating[e]=!0};for(let e in E){let t=R[e],r=S[e];if(!d.hasOwnProperty(e))(eP(t)&&eP(r)?e1(t,r):t===r)?void 0!==t&&c.has(e)?T(e):g.protectedKeys[e]=!0:void 0!==t?T(e):c.add(e)}g.prevProp=v,g.prevResolvedValues=R,g.isActive&&(d={...d,...R}),n&&e.blockInitialAnimation&&(_=!1),_&&(!P||w)&&u.push(...j.map(e=>({animation:e,options:{type:y,...i}})))}if(c.size){let t={};c.forEach(r=>{let n=e.getBaseTarget(r);void 0!==n&&(t[r]=n)}),u.push({animation:t})}let y=!!u.length;return n&&(!1===s.initial||s.initial===s.animate)&&!e.manuallyAnimateOnMount&&(y=!1),n=!1,y?t(u):Promise.resolve()}return{animateChanges:i,setActive:function(t,n,o){var a;if(r[t].isActive===n)return Promise.resolve();null===(a=e.variantChildren)||void 0===a||a.forEach(e=>{var r;return null===(r=e.animationState)||void 0===r?void 0:r.setActive(t,n)}),r[t].isActive=n;let s=i(o,t);for(let e in r)r[e].protectedKeys={};return s},setAnimateFunction:function(r){t=r(e)},getState:()=>r}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();this.unmount(),h(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}}let rF=0;class rU extends eB{constructor(){super(...arguments),this.id=rF++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t,custom:r}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let o=this.node.animationState.setActive("exit",!e,{custom:null!=r?r:this.node.getProps().custom});t&&!e&&o.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}let rV=(e,t)=>Math.abs(e-t);class rI{constructor(e,t,{transformPagePoint:r,contextWindow:n,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rH(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){return Math.sqrt(rV(e.x,t.x)**2+rV(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;let{point:n}=e,{timestamp:o}=eS.frameData;this.history.push({...n,timestamp:o});let{onStart:i,onMove:a}=this.handlers;t||(i&&i(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rB(t,this.transformPagePoint),eS.Wi.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:o}=this.handlers;if(this.dragSnapToOrigin&&o&&o(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let i=rH("pointercancel"===e.type?this.lastMoveEventInfo:rB(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,i),n&&n(e,i)},!eM(e))return;this.dragSnapToOrigin=o,this.handlers=t,this.transformPagePoint=r,this.contextWindow=n||window;let i=rB(eA(e),this.transformPagePoint),{point:a}=i,{timestamp:s}=eS.frameData;this.history=[{...a,timestamp:s}];let{onSessionStart:l}=t;l&&l(e,rH(i,this.history)),this.removeListeners=eN(eD(this.contextWindow,"pointermove",this.handlePointerMove),eD(this.contextWindow,"pointerup",this.handlePointerUp),eD(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),(0,eS.Pn)(this.updatePoint)}}function rB(e,t){return t?{point:t(e.point)}:e}function rz(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rH({point:e},t){return{point:e,delta:rz(e,rW(t)),offset:rz(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null,o=rW(e);for(;r>=0&&(n=e[r],!(o.timestamp-n.timestamp>e7(.1)));)r--;if(!n)return{x:0,y:0};let i=e3(o.timestamp-n.timestamp);if(0===i)return{x:0,y:0};let a={x:(o.x-n.x)/i,y:(o.y-n.y)/i};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,0)}}function rW(e){return e[e.length-1]}function r$(e){return e.max-e.min}function rG(e,t=0,r=.01){return Math.abs(e-t)<=r}function rK(e,t,r,n=.5){e.origin=n,e.originPoint=tj(t.min,t.max,e.origin),e.scale=r$(r)/r$(t),(rG(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=tj(r.min,r.max,e.origin)-e.originPoint,(rG(e.translate)||isNaN(e.translate))&&(e.translate=0)}function rY(e,t,r,n){rK(e.x,t.x,r.x,n?n.originX:void 0),rK(e.y,t.y,r.y,n?n.originY:void 0)}function rX(e,t,r){e.min=r.min+t.min,e.max=e.min+r$(t)}function rZ(e,t,r){e.min=t.min-r.min,e.max=e.min+r$(t)}function rq(e,t,r){rZ(e.x,t.x,r.x),rZ(e.y,t.y,r.y)}function rJ(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function rQ(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}function r0(e,t,r){return{min:r1(e,t),max:r1(e,r)}}function r1(e,t){return"number"==typeof e?e:e[t]||0}let r2=()=>({translate:0,scale:1,origin:0,originPoint:0}),r5=()=>({x:r2(),y:r2()}),r7=()=>({min:0,max:0}),r3=()=>({x:r7(),y:r7()});function r4(e){return[e("x"),e("y")]}function r8({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function r6(e){return void 0===e||1===e}function r9({scale:e,scaleX:t,scaleY:r}){return!r6(e)||!r6(t)||!r6(r)}function ne(e){return r9(e)||nt(e)||e.z||e.rotate||e.rotateX||e.rotateY}function nt(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}function nr(e,t,r,n,o){return void 0!==o&&(e=n+o*(e-n)),n+r*(e-n)+t}function nn(e,t=0,r=1,n,o){e.min=nr(e.min,t,r,n,o),e.max=nr(e.max,t,r,n,o)}function no(e,{x:t,y:r}){nn(e.x,t.translate,t.scale,t.originPoint),nn(e.y,r.translate,r.scale,r.originPoint)}function ni(e){return Number.isInteger(e)?e:e>1.0000000000001||e<.999999999999?e:1}function na(e,t){e.min=e.min+t,e.max=e.max+t}function ns(e,t,[r,n,o]){let i=void 0!==t[o]?t[o]:.5,a=tj(e.min,e.max,i);nn(e,t[r],t[n],a,t.scale)}let nl=["x","scaleX","originX"],nu=["y","scaleY","originY"];function nc(e,t){ns(e.x,t,nl),ns(e.y,t,nu)}function nd(e,t){return r8(function(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let nf=({current:e})=>e?e.ownerDocument.defaultView:null,nh=new WeakMap;class np{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=r3(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new rI(e,{onSessionStart:e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(eA(e,"page").point)},onStart:(e,t)=>{let{drag:r,dragPropagation:n,onDragStart:o}=this.getProps();if(r&&!n&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=eV(r),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),r4(e=>{let t=this.getAxisMotionValue(e).get()||0;if(X.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[e];if(n){let e=r$(n);t=parseFloat(t)/100*e}}}this.originPoint[e]=t}),o&&eS.Wi.update(()=>o(e,t),!1,!0);let{animationState:i}=this.visualElement;i&&i.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:o,onDrag:i}=this.getProps();if(!r&&!this.openGlobalLock)return;let{offset:a}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(a),null!==this.currentDirection&&o&&o(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),i&&i(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>r4(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:nf(this.visualElement)})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:n}=t;this.startAnimation(n);let{onDragEnd:o}=this.getProps();o&&eS.Wi.update(()=>o(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:n}=this.getProps();if(!r||!nm(e,n,this.currentDirection))return;let o=this.getAxisMotionValue(e),i=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(i=function(e,{min:t,max:r},n){return void 0!==t&&e<t?e=n?tj(t,e,n.min):Math.max(e,t):void 0!==r&&e>r&&(e=n?tj(r,e,n.max):Math.min(e,r)),e}(i,this.constraints[e],this.elastic[e])),o.set(i)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:r}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,o=this.constraints;t&&d(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=function(e,{top:t,left:r,bottom:n,right:o}){return{x:rJ(e.x,r,o),y:rJ(e.y,t,n)}}(n.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:r0(e,"left","right"),y:r0(e,"top","bottom")}}(r),o!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&r4(e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!d(t))return!1;let n=t.current;(0,e5.k)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:o}=this.visualElement;if(!o||!o.layout)return!1;let i=function(e,t,r){let n=nd(e,r),{scroll:o}=t;return o&&(na(n.x,o.offset.x),na(n.y,o.offset.y)),n}(n,o.root,this.visualElement.getTransformPagePoint()),a={x:rQ((e=o.layout.layoutBox).x,i.x),y:rQ(e.y,i.y)};if(r){let e=r(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=r8(e))}return a}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:n,dragTransition:o,dragSnapToOrigin:i,onDragTransitionEnd:a}=this.getProps(),s=this.constraints||{};return Promise.all(r4(a=>{if(!nm(a,t,this.currentDirection))return;let l=s&&s[a]||{};i&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...o,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return r.start(rm(e,r,0,t))}stopAnimation(){r4(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){r4(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){let t="_drag"+e.toUpperCase(),r=this.visualElement.getProps();return r[t]||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){r4(t=>{let{drag:r}=this.getProps();if(!nm(t,r,this.currentDirection))return;let{projection:n}=this.visualElement,o=this.getAxisMotionValue(t);if(n&&n.layout){let{min:r,max:i}=n.layout.layoutBox[t];o.set(e[t]-tj(r,i,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!d(t)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};r4(e=>{let t=this.getAxisMotionValue(e);if(t){let r=t.get();n[e]=function(e,t){let r=.5,n=r$(e),o=r$(t);return o>n?r=t$(t.min,t.max-n,e.min):n>o&&(r=t$(e.min,e.max-o,t.min)),U(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),r4(t=>{if(!nm(t,e,null))return;let r=this.getAxisMotionValue(t),{min:o,max:i}=this.constraints[t];r.set(tj(o,i,n[t]))})}addListeners(){if(!this.visualElement.current)return;nh.set(this.visualElement,this);let e=eD(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();d(e)&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,n=r.addEventListener("measure",t);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),t();let o=eO(window,"resize",()=>this.scalePositionWithinConstraints()),i=r.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(r4(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{o(),e(),n(),i&&i()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:o=!1,dragElastic:i=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:o,dragElastic:i,dragMomentum:a}}}function nm(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}class ny extends eB{constructor(e){super(e),this.removeGroupControls=eG.Z,this.removeListeners=eG.Z,this.controls=new np(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||eG.Z}unmount(){this.removeGroupControls(),this.removeListeners()}}let ng=e=>(t,r)=>{e&&eS.Wi.update(()=>e(t,r))};class nv extends eB{constructor(){super(...arguments),this.removePointerDownListener=eG.Z}onPointerDown(e){this.session=new rI(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nf(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:ng(e),onStart:ng(t),onMove:r,onEnd:(e,t)=>{delete this.session,n&&eS.Wi.update(()=>n(e,t))}}}mount(){this.removePointerDownListener=eD(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let nb={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nx(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let nP={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!Z.test(e))return e;e=parseFloat(e)}let r=nx(e,t.target.x),n=nx(e,t.target.y);return`${r}% ${n}%`}};class n_ extends n.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:o}=e;Object.assign(E,nj),o&&(t.group&&t.group.add(o),r&&r.register&&n&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),nb.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:n,isPresent:o}=this.props,i=r.projection;return i&&(i.isPresent=o,n||e.layoutDependency!==t||void 0===t?i.willUpdate():this.safeToRemove(),e.isPresent===o||(o?i.promote():i.relegate()||eS.Wi.postRender(()=>{let e=i.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function nw(e){let[t,r]=function(){let e=(0,n.useContext)(a.O);if(null===e)return[!0,null];let{isPresent:t,onExitComplete:r,register:o}=e,i=(0,n.useId)();return(0,n.useEffect)(()=>o(i),[]),!t&&r?[!1,()=>r&&r(i)]:[!0]}(),o=(0,n.useContext)(_.p);return n.createElement(n_,{...e,layoutGroup:o,switchLayoutGroup:(0,n.useContext)(w),isPresent:t,safeToRemove:r})}let nj={borderRadius:{...nP,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nP,borderTopRightRadius:nP,borderBottomLeftRadius:nP,borderBottomRightRadius:nP,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let n=tV.parse(e);if(n.length>5)return e;let o=tV.createTransformer(e),i="number"!=typeof n[0]?1:0,a=r.x.scale*t.x,s=r.y.scale*t.y;n[0+i]/=a,n[1+i]/=s;let l=tj(a,s,.5);return"number"==typeof n[2+i]&&(n[2+i]/=l),"number"==typeof n[3+i]&&(n[3+i]/=l),o(n)}}},nR=["TopLeft","TopRight","BottomLeft","BottomRight"],nS=nR.length,nE=e=>"string"==typeof e?parseFloat(e):e,nT=e=>"number"==typeof e||Z.test(e);function nO(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let nM=nC(0,.5,tu),nA=nC(.5,.95,eG.Z);function nC(e,t,r){return n=>n<e?0:n>t?1:r(t$(e,t,n))}function nD(e,t){e.min=t.min,e.max=t.max}function nk(e,t){nD(e.x,t.x),nD(e.y,t.y)}function nN(e,t,r,n,o){return e-=t,e=n+1/r*(e-n),void 0!==o&&(e=n+1/o*(e-n)),e}function nL(e,t,[r,n,o],i,a){!function(e,t=0,r=1,n=.5,o,i=e,a=e){if(X.test(t)&&(t=parseFloat(t),t=tj(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let s=tj(i.min,i.max,n);e===i&&(s-=t),e.min=nN(e.min,t,r,s,o),e.max=nN(e.max,t,r,s,o)}(e,t[r],t[n],t[o],t.scale,i,a)}let nF=["x","scaleX","originX"],nU=["y","scaleY","originY"];function nV(e,t,r,n){nL(e.x,t,nF,r?r.x:void 0,n?n.x:void 0),nL(e.y,t,nU,r?r.y:void 0,n?n.y:void 0)}function nI(e){return 0===e.translate&&1===e.scale}function nB(e){return nI(e.x)&&nI(e.y)}function nz(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function nH(e){return r$(e.x)/r$(e.y)}class nW{constructor(){this.members=[]}add(e){rv(this.members,e),e.scheduleRender()}remove(e){if(rb(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function n$(e,t,r){let n="",o=e.x.translate/t.x,i=e.y.translate/t.y;if((o||i)&&(n=`translate3d(${o}px, ${i}px, 0) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),r){let{rotate:e,rotateX:t,rotateY:o}=r;e&&(n+=`rotate(${e}deg) `),t&&(n+=`rotateX(${t}deg) `),o&&(n+=`rotateY(${o}deg) `)}let a=e.x.scale*t.x,s=e.y.scale*t.y;return(1!==a||1!==s)&&(n+=`scale(${a}, ${s})`),n||"none"}let nG=(e,t)=>e.depth-t.depth;class nK{constructor(){this.children=[],this.isDirty=!1}add(e){rv(this.children,e),this.isDirty=!0}remove(e){rb(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(nG),this.isDirty=!1,this.children.forEach(e)}}let nY=["","X","Y","Z"],nX={visibility:"hidden"},nZ=0,nq={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function nJ({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:o}){return class{constructor(e={},r=null==t?void 0:t()){this.id=nZ++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,nq.totalNodes=nq.resolvedTargetDeltas=nq.recalculatedProjection=0,this.nodes.forEach(n1),this.nodes.forEach(n6),this.nodes.forEach(n9),this.nodes.forEach(n2),window.MotionDebug&&window.MotionDebug.record(nq)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new nK)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new rx),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,r=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:n,layout:o,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(o||n)&&(this.isLayoutDirty=!0),e){let r;let n=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=performance.now(),n=({timestamp:t})=>{let o=t-r;o>=250&&((0,eS.Pn)(n),e(o-250))};return eS.Wi.read(n,!0),()=>(0,eS.Pn)(n)}(n,0),nb.hasAnimatedSinceResize&&(nb.hasAnimatedSinceResize=!1,this.nodes.forEach(n8))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||o)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:r,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||oi,{onLayoutAnimationStart:a,onLayoutAnimationComplete:s}=i.getProps(),l=!this.targetLayout||!nz(this.targetLayout,n)||r,u=!t&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,u);let t={...rh(o,"layout"),onPlay:a,onComplete:s};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||n8(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,eS.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(oe),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(n7);return}this.isUpdating||this.nodes.forEach(n3),this.isUpdating=!1,this.nodes.forEach(n4),this.nodes.forEach(nQ),this.nodes.forEach(n0),this.clearAllSnapshots();let e=performance.now();eS.frameData.delta=U(0,1e3/60,e-eS.frameData.timestamp),eS.frameData.timestamp=e,eS.frameData.isProcessing=!0,eS.S6.update.process(eS.frameData),eS.S6.preRender.process(eS.frameData),eS.S6.render.process(eS.frameData),eS.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(n5),this.sharedNodes.forEach(ot)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,eS.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){eS.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=r3(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:n(this.instance),offset:r(this.instance)})}resetTransform(){if(!o)return;let e=this.isLayoutDirty||this.shouldResetTransform,t=this.projectionDelta&&!nB(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,i=n!==this.prevTransformTemplateValue;e&&(t||ne(this.latestValues)||i)&&(o(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),n=this.removeElementScroll(r);return e&&(n=this.removeTransform(n)),ol((t=n).x),ol(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return r3();let t=e.measureViewportBox(),{scroll:r}=this.root;return r&&(na(t.x,r.offset.x),na(t.y,r.offset.y)),t}removeElementScroll(e){let t=r3();nk(t,e);for(let r=0;r<this.path.length;r++){let n=this.path[r],{scroll:o,options:i}=n;if(n!==this.root&&o&&i.layoutScroll){if(o.isRoot){nk(t,e);let{scroll:r}=this.root;r&&(na(t.x,-r.offset.x),na(t.y,-r.offset.y))}na(t.x,o.offset.x),na(t.y,o.offset.y)}}return t}applyTransform(e,t=!1){let r=r3();nk(r,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&nc(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),ne(n.latestValues)&&nc(r,n.latestValues)}return ne(this.latestValues)&&nc(r,this.latestValues),r}removeTransform(e){let t=r3();nk(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!ne(r.latestValues))continue;r9(r.latestValues)&&r.updateSnapshot();let n=r3();nk(n,r.measurePageBox()),nV(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return ne(this.latestValues)&&nV(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==eS.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,r,n,o;let i=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=i.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=i.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=i.isSharedProjectionDirty);let a=!!this.resumingFrom||this!==i;if(!(e||a&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:s,layoutId:l}=this.options;if(this.layout&&(s||l)){if(this.resolvedRelativeTargetAt=eS.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=r3(),this.relativeTargetOrigin=r3(),rq(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nk(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=r3(),this.targetWithTransforms=r3()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),r=this.target,n=this.relativeTarget,o=this.relativeParent.target,rX(r.x,n.x,o.x),rX(r.y,n.y,o.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nk(this.target,this.layout.layoutBox),no(this.target,this.targetDelta)):nk(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=r3(),this.relativeTargetOrigin=r3(),rq(this.relativeTargetOrigin,this.target,e.target),nk(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}nq.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||r9(this.parent.latestValues)||nt(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),r=!!this.resumingFrom||this!==t,n=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(n=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===eS.frameData.timestamp&&(n=!1),n)return;let{layout:o,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(o||i))return;nk(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,s=this.treeScale.y;(function(e,t,r,n=!1){let o,i;let a=r.length;if(a){t.x=t.y=1;for(let s=0;s<a;s++){i=(o=r[s]).projectionDelta;let a=o.instance;(!a||!a.style||"contents"!==a.style.display)&&(n&&o.options.layoutScroll&&o.scroll&&o!==o.root&&nc(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),i&&(t.x*=i.x.scale,t.y*=i.y.scale,no(e,i)),n&&ne(o.latestValues)&&nc(e,o.latestValues))}t.x=ni(t.x),t.y=ni(t.y)}})(this.layoutCorrected,this.treeScale,this.path,r),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox);let{target:l}=t;if(!l){this.projectionTransform&&(this.projectionDelta=r5(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=r5(),this.projectionDeltaWithTransform=r5());let u=this.projectionTransform;rY(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=n$(this.projectionDelta,this.treeScale),(this.projectionTransform!==u||this.treeScale.x!==a||this.treeScale.y!==s)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),nq.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e,t=!1){let r;let n=this.snapshot,o=n?n.latestValues:{},i={...this.latestValues},a=r5();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let s=r3(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(oo));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(or(a.x,e.x,n),or(a.y,e.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,f,h,p;rq(s,this.layout.layoutBox,this.relativeParent.layout.layoutBox),h=this.relativeTarget,p=this.relativeTargetOrigin,on(h.x,p.x,s.x,n),on(h.y,p.y,s.y,n),r&&(u=this.relativeTarget,f=r,u.x.min===f.x.min&&u.x.max===f.x.max&&u.y.min===f.y.min&&u.y.max===f.y.max)&&(this.isProjectionDirty=!1),r||(r=r3()),nk(r,this.relativeTarget)}l&&(this.animationValues=i,function(e,t,r,n,o,i){o?(e.opacity=tj(0,void 0!==r.opacity?r.opacity:1,nM(n)),e.opacityExit=tj(void 0!==t.opacity?t.opacity:1,0,nA(n))):i&&(e.opacity=tj(void 0!==t.opacity?t.opacity:1,void 0!==r.opacity?r.opacity:1,n));for(let o=0;o<nS;o++){let i=`border${nR[o]}Radius`,a=nO(t,i),s=nO(r,i);(void 0!==a||void 0!==s)&&(a||(a=0),s||(s=0),0===a||0===s||nT(a)===nT(s)?(e[i]=Math.max(tj(nE(a),nE(s),n),0),(X.test(s)||X.test(a))&&(e[i]+="%")):e[i]=s)}(t.rotate||r.rotate)&&(e.rotate=tj(t.rotate||0,r.rotate||0,n))}(i,o,this.latestValues,n,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,eS.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=eS.Wi.update(()=>{nb.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,r){let n=A(0)?0:rj(0);return n.start(rm("",n,1e3,r)),n.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:n,latestValues:o}=e;if(t&&r&&n){if(this!==e&&this.layout&&n&&ou(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||r3();let t=r$(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let n=r$(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+n}nk(t,r),nc(t,o),rY(this.projectionDeltaWithTransform,this.layoutCorrected,t,o)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nW),this.sharedNodes.get(e).add(t);let r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let n=this.getStack();n&&n.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.rotate||r.rotateX||r.rotateY||r.rotateZ)&&(t=!0),!t)return;let n={};for(let t=0;t<nY.length;t++){let o="rotate"+nY[t];r[o]&&(n[o]=r[o],e.setStaticValue(o,0))}for(let t in e.render(),n)e.setStaticValue(t,n[t]);e.scheduleRender()}getProjectionStyles(e){var t,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return nX;let n={visibility:""},o=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,n.opacity="",n.pointerEvents=ej(null==e?void 0:e.pointerEvents)||"",n.transform=o?o(this.latestValues,""):"none",n;let i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=ej(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!ne(this.latestValues)&&(t.transform=o?o({},""):"none",this.hasProjected=!1),t}let a=i.animationValues||i.latestValues;this.applyTransformsToTarget(),n.transform=n$(this.projectionDeltaWithTransform,this.treeScale,a),o&&(n.transform=o(a,n.transform));let{x:s,y:l}=this.projectionDelta;for(let e in n.transformOrigin=`${100*s.origin}% ${100*l.origin}% 0`,i.animationValues?n.opacity=i===this?null!==(r=null!==(t=a.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:a.opacityExit:n.opacity=i===this?void 0!==a.opacity?a.opacity:"":void 0!==a.opacityExit?a.opacityExit:0,E){if(void 0===a[e])continue;let{correct:t,applyTo:r}=E[e],o="none"===n.transform?a[e]:t(a[e],i);if(r){let e=r.length;for(let t=0;t<e;t++)n[r[t]]=o}else n[e]=o}return this.options.layoutId&&(n.pointerEvents=i===this?ej(null==e?void 0:e.pointerEvents)||"":"none"),n}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(n7),this.root.sharedNodes.clear()}}}function nQ(e){e.updateLayout()}function n0(e){var t;let r=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&r&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:n}=e.layout,{animationType:o}=e.options,i=r.source!==e.layout.source;"size"===o?r4(e=>{let n=i?r.measuredBox[e]:r.layoutBox[e],o=r$(n);n.min=t[e].min,n.max=n.min+o}):ou(o,r.layoutBox,t)&&r4(n=>{let o=i?r.measuredBox[n]:r.layoutBox[n],a=r$(t[n]);o.max=o.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+a)});let a=r5();rY(a,t,r.layoutBox);let s=r5();i?rY(s,e.applyTransform(n,!0),r.measuredBox):rY(s,t,r.layoutBox);let l=!nB(a),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:o,layout:i}=n;if(o&&i){let a=r3();rq(a,r.layoutBox,o.layoutBox);let s=r3();rq(s,t,i.layoutBox),nz(a,s)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=a,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:r,delta:s,layoutDelta:a,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function n1(e){nq.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function n2(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function n5(e){e.clearSnapshot()}function n7(e){e.clearMeasurements()}function n3(e){e.isLayoutDirty=!1}function n4(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function n8(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function n6(e){e.resolveTargetDelta()}function n9(e){e.calcProjection()}function oe(e){e.resetRotation()}function ot(e){e.removeLeadSnapshot()}function or(e,t,r){e.translate=tj(t.translate,0,r),e.scale=tj(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function on(e,t,r,n){e.min=tj(t.min,r.min,n),e.max=tj(t.max,r.max,n)}function oo(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let oi={duration:.45,ease:[.4,0,.1,1]},oa=e=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(e),os=oa("applewebkit/")&&!oa("chrome/")?Math.round:eG.Z;function ol(e){e.min=os(e.min),e.max=os(e.max)}function ou(e,t,r){return"position"===e||"preserve-aspect"===e&&!rG(nH(t),nH(r),.2)}let oc=nJ({attachResizeListener:(e,t)=>eO(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),od={current:void 0},of=nJ({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!od.current){let e=new oc({});e.mount(window),e.setOptions({layoutScroll:!0}),od.current=e}return od.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position}),oh=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function op(e,t,r=1){(0,e5.k)(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,o]=function(e){let t=oh.exec(e);if(!t)return[,];let[,r,n]=t;return[r,n]}(e);if(!n)return;let i=window.getComputedStyle(t).getPropertyValue(n);if(i){let e=i.trim();return rg(e)?parseFloat(e):e}return L(o)?op(o,t,r+1):o}let om=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),oy=e=>om.has(e),og=e=>Object.keys(e).some(oy),ov=e=>e===V||e===Z,ob=(e,t)=>parseFloat(e.split(", ")[t]),ox=(e,t)=>(r,{transform:n})=>{if("none"===n||!n)return 0;let o=n.match(/^matrix3d\((.+)\)$/);if(o)return ob(o[1],t);{let t=n.match(/^matrix\((.+)\)$/);return t?ob(t[1],e):0}},oP=new Set(["x","y","z"]),o_=T.filter(e=>!oP.has(e)),ow={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:ox(4,13),y:ox(5,14)};ow.translateX=ow.x,ow.translateY=ow.y;let oj=(e,t,r)=>{let n=t.measureViewportBox(),o=getComputedStyle(t.current),{display:i}=o,a={};"none"===i&&t.setStaticValue("display",e.display||"block"),r.forEach(e=>{a[e]=ow[e](n,o)}),t.render();let s=t.measureViewportBox();return r.forEach(r=>{let n=t.getValue(r);n&&n.jump(a[r]),e[r]=ow[r](s,o)}),e},oR=(e,t,r={},n={})=>{t={...t},n={...n};let o=Object.keys(t).filter(oy),i=[],a=!1,s=[];if(o.forEach(o=>{let l;let u=e.getValue(o);if(!e.hasValue(o))return;let c=r[o],d=rE(c),f=t[o];if(eP(f)){let e=f.length,t=null===f[0]?1:0;d=rE(c=f[t]);for(let r=t;r<e&&null!==f[r];r++)l?(0,e5.k)(rE(f[r])===l,"All keyframes must be of the same type"):(l=rE(f[r]),(0,e5.k)(l===d||ov(d)&&ov(l),"Keyframes must be of the same dimension as the current value"))}else l=rE(f);if(d!==l){if(ov(d)&&ov(l)){let e=u.get();"string"==typeof e&&u.set(parseFloat(e)),"string"==typeof f?t[o]=parseFloat(f):Array.isArray(f)&&l===Z&&(t[o]=f.map(parseFloat))}else(null==d?void 0:d.transform)&&(null==l?void 0:l.transform)&&(0===c||0===f)?0===c?u.set(l.transform(c)):t[o]=d.transform(f):(a||(i=function(e){let t=[];return o_.forEach(r=>{let n=e.getValue(r);void 0!==n&&(t.push([r,n.get()]),n.set(r.startsWith("scale")?1:0))}),t.length&&e.render(),t}(e),a=!0),s.push(o),n[o]=void 0!==n[o]?n[o]:t[o],u.jump(f))}}),!s.length)return{target:t,transitionEnd:n};{let r=s.indexOf("height")>=0?window.pageYOffset:null,o=oj(t,e,s);return i.length&&i.forEach(([t,r])=>{e.getValue(t).set(r)}),e.render(),P.j&&null!==r&&window.scrollTo({top:r}),{target:o,transitionEnd:n}}},oS=(e,t,r,n)=>{let o=function(e,{...t},r){let n=e.current;if(!(n instanceof Element))return{target:t,transitionEnd:r};for(let o in r&&(r={...r}),e.values.forEach(e=>{let t=e.get();if(!L(t))return;let r=op(t,n);r&&e.set(r)}),t){let e=t[o];if(!L(e))continue;let i=op(e,n);i&&(t[o]=i,r||(r={}),void 0===r[o]&&(r[o]=e))}return{target:t,transitionEnd:r}}(e,t,n);return function(e,t,r,n){return og(t)?oR(e,t,r,n):{target:t,transitionEnd:n}}(e,t=o.target,r,n=o.transitionEnd)},oE={current:null},oT={current:!1},oO=new WeakMap,oM=Object.keys(x),oA=oM.length,oC=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],oD=m.length;class ok{constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,visualState:o},i={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>eS.Wi.render(this.render,!1,!0);let{latestValues:a,renderState:s}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=s,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=i,this.isControllingVariants=y(t),this.isVariantNode=g(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:l,...u}=this.scrapeMotionValuesFromProps(t,{});for(let e in u){let t=u[e];void 0!==a[e]&&A(t)&&(t.set(a[e],!1),ry(l)&&l.add(e))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,oO.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),oT.current||function(){if(oT.current=!0,P.j){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>oE.current=e.matches;e.addListener(t),t()}else oE.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||oE.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in oO.delete(this.current),this.projection&&this.projection.unmount(),(0,eS.Pn)(this.notifyUpdate),(0,eS.Pn)(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){let r=O.has(e),n=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&eS.Wi.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{n(),o()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures({children:e,...t},r,n,o){let i,a;for(let e=0;e<oA;e++){let r=oM[e],{isEnabled:n,Feature:o,ProjectionNode:s,MeasureLayout:l}=x[r];s&&(i=s),n(t)&&(!this.features[r]&&o&&(this.features[r]=new o(this)),l&&(a=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&i){this.projection=new i(this.latestValues,this.parent&&this.parent.projection);let{layoutId:e,layout:r,drag:n,dragConstraints:a,layoutScroll:s,layoutRoot:l}=t;this.projection.setOptions({layoutId:e,layout:r,alwaysMeasureLayout:!!n||a&&d(a),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof r?r:"both",initialPromotionConfig:o,layoutScroll:s,layoutRoot:l})}return a}updateFeatures(){for(let e in this.features){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):r3()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e,t=!0){return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<oC.length;t++){let r=oC[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=e["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(e,t,r){let{willChange:n}=t;for(let o in t){let i=t[o],a=r[o];if(A(i))e.addValue(o,i),ry(n)&&n.add(o);else if(A(a))e.addValue(o,rj(i,{owner:e})),ry(n)&&n.remove(o);else if(a!==i){if(e.hasValue(o)){let t=e.getValue(o);t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(o);e.addValue(o,rj(void 0!==t?t:i,{owner:e}))}}}for(let n in r)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}let t={};for(let e=0;e<oD;e++){let r=m[e],n=this.props[r];(f(n)||!1===n)&&(t[r]=n)}return t}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=rj(t,{owner:this}),this.addValue(e,r)),r}readValue(e){var t;return void 0===this.latestValues[e]&&this.current?null!==(t=this.getBaseTargetFromProps(this.props,e))&&void 0!==t?t:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let{initial:r}=this.props,n="string"==typeof r||"object"==typeof r?null===(t=eb(this.props,r))||void 0===t?void 0:t[e]:void 0;if(r&&void 0!==n)return n;let o=this.getBaseTargetFromProps(this.props,e);return void 0===o||A(o)?void 0!==this.initialValues[e]&&void 0===n?void 0:this.baseTarget[e]:o}on(e,t){return this.events[e]||(this.events[e]=new rx),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class oN extends ok{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:t,...r},{transformValues:n},o){let i=function(e,t,r){let n={};for(let o in e){let e=function(e,t){if(t)return(t[e]||t.default||t).from}(o,t);if(void 0!==e)n[o]=e;else{let e=r.getValue(o);e&&(n[o]=e.get())}}return n}(r,e||{},this);if(n&&(t&&(t=n(t)),r&&(r=n(r)),i&&(i=n(i))),o){!function(e,t,r){var n,o;let i=Object.keys(t).filter(t=>!e.hasValue(t)),a=i.length;if(a)for(let s=0;s<a;s++){let a=i[s],l=t[a],u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(o=null!==(n=r[a])&&void 0!==n?n:e.readValue(a))&&void 0!==o?o:t[a]),null!=u&&("string"==typeof u&&(rg(u)||rf(u))?u=parseFloat(u):!rO(u)&&tV.test(l)&&(u=rd(a,l)),e.addValue(a,rj(u,{owner:e})),void 0===r[a]&&(r[a]=u),null!==u&&e.setBaseTarget(a,u))}}(this,r,i);let e=oS(this,r,i,t);t=e.transitionEnd,r=e.target}return{transition:e,transitionEnd:t,...r}}}class oL extends oN{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,t){if(O.has(t)){let e=rc(t);return e&&e.default||0}{let r=window.getComputedStyle(e),n=(N(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return nd(e,t)}build(e,t,r,n){er(e,t,r,n.transformTemplate)}scrapeMotionValuesFromProps(e,t){return eg(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;A(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}renderInstance(e,t,r,n){ep(e,t,r,n)}}class oF extends oN{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(O.has(t)){let e=rc(t);return e&&e.default||0}return t=em.has(t)?t:u(t),e.getAttribute(t)}measureInstanceViewportBox(){return r3()}scrapeMotionValuesFromProps(e,t){return ev(e,t)}build(e,t,r,n){ed(e,t,r,this.isSVGTag,n.transformTemplate)}renderInstance(e,t,r,n){ey(e,t,r,n)}mount(e){this.isSVGTag=eh(e.tagName),super.mount(e)}}let oU=(e,t)=>S(e)?new oF(t,{enableHardwareAcceleration:!1}):new oL(t,{enableHardwareAcceleration:!0}),oV={animation:{Feature:rL},exit:{Feature:rU},inView:{Feature:e0},tap:{Feature:eY},focus:{Feature:eW},hover:{Feature:eH},pan:{Feature:nv},drag:{Feature:ny,ProjectionNode:of,MeasureLayout:nw},layout:{ProjectionNode:of,MeasureLayout:nw}},oI=function(e){function t(t,r={}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:u,Component:h}){e&&function(e){for(let t in e)x[t]={...x[t],...e[t]}}(e);let p=(0,n.forwardRef)(function(p,m){var g;let b;let x={...(0,n.useContext)(o),...p,layoutId:function({layoutId:e}){let t=(0,n.useContext)(_.p).id;return t&&void 0!==e?t+"-"+e:e}(p)},{isStatic:j}=x,R=function(e){let{initial:t,animate:r}=function(e,t){if(y(e)){let{initial:t,animate:r}=e;return{initial:!1===t||f(t)?t:void 0,animate:f(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,n.useContext)(i));return(0,n.useMemo)(()=>({initial:t,animate:r}),[v(t),v(r)])}(p),S=u(p,j);if(!j&&P.j){R.visualElement=function(e,t,r,u){let{visualElement:d}=(0,n.useContext)(i),f=(0,n.useContext)(l),h=(0,n.useContext)(a.O),p=(0,n.useContext)(o).reducedMotion,m=(0,n.useRef)();u=u||f.renderer,!m.current&&u&&(m.current=u(e,{visualState:t,parent:d,props:r,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:p}));let y=m.current;(0,n.useInsertionEffect)(()=>{y&&y.update(r,h)});let g=(0,n.useRef)(!!(r[c]&&!window.HandoffComplete));return(0,s.L)(()=>{y&&(y.render(),g.current&&y.animationState&&y.animationState.animateChanges())}),(0,n.useEffect)(()=>{y&&(y.updateFeatures(),!g.current&&y.animationState&&y.animationState.animateChanges(),g.current&&(g.current=!1,window.HandoffComplete=!0))}),y}(h,S,x,t);let r=(0,n.useContext)(w),u=(0,n.useContext)(l).strict;R.visualElement&&(b=R.visualElement.loadFeatures(x,u,e,r))}return n.createElement(i.Provider,{value:R},b&&R.visualElement?n.createElement(b,{visualElement:R.visualElement,...x}):null,r(h,p,(g=R.visualElement,(0,n.useCallback)(e=>{e&&S.mount&&S.mount(e),g&&(e?g.mount(e):g.unmount()),m&&("function"==typeof m?m(e):d(m)&&(m.current=e))},[g])),S,j,R.visualElement))});return p[j]=h,p}(e(t,r))}if("undefined"==typeof Proxy)return t;let r=new Map;return new Proxy(t,{get:(e,n)=>(r.has(n)||r.set(n,t(n)),r.get(n))})}((e,t)=>(function(e,{forwardMotionProps:t=!1},r,o){return{...S(e)?eE:eT,preloadedFeatures:r,useRender:function(e=!1){return(t,r,o,{latestValues:i},a)=>{let s=(S(t)?function(e,t,r,o){let i=(0,n.useMemo)(()=>{let r=ef();return ed(r,t,{enableHardwareAcceleration:!1},eh(o),e.transformTemplate),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};eo(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t,r){let o={},i=function(e,t,r){let o=e.style||{},i={};return eo(i,o,e),Object.assign(i,function({transformTemplate:e},t,r){return(0,n.useMemo)(()=>{let n=en();return er(n,t,{enableHardwareAcceleration:!r},e),Object.assign({},n.vars,n.style)},[t])}(e,t,r)),e.transformValues?e.transformValues(i):i}(e,t,r);return e.drag&&!1!==e.dragListener&&(o.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(o.tabIndex=0),o.style=i,o})(r,i,a,t),l={...function(e,t,r){let n={};for(let o in e)("values"!==o||"object"!=typeof e.values)&&(es(o)||!0===r&&ea(o)||!t&&!ea(o)||e.draggable&&o.startsWith("onDrag"))&&(n[o]=e[o]);return n}(r,"string"==typeof t,e),...s,ref:o},{children:u}=r,c=(0,n.useMemo)(()=>A(u)?u.get():u,[u]);return(0,n.createElement)(t,{...l,children:c})}}(t),createVisualElement:o,Component:e}})(e,t,oV,oU))},4673:(e,t,r)=>{"use strict";r.d(t,{K:()=>o,k:()=>i});var n=r(4380);let o=n.Z,i=n.Z},8263:(e,t,r)=>{"use strict";r.d(t,{j:()=>n});let n="undefined"!=typeof document},4380:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=e=>e},4749:(e,t,r)=>{"use strict";r.d(t,{h:()=>o});var n=r(7577);function o(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}},2482:(e,t,r)=>{"use strict";r.d(t,{L:()=>o});var n=r(7577);let o=r(8263).j?n.useLayoutEffect:n.useEffect},4831:(e,t,r)=>{"use strict";r.d(t,{f:()=>s});var n=r(7577),o=["light","dark"],i="(prefers-color-scheme: dark)",a=n.createContext(void 0),s=e=>n.useContext(a)?e.children:n.createElement(u,{...e}),l=["light","dark"],u=({forcedTheme:e,disableTransitionOnChange:t=!1,enableSystem:r=!0,enableColorScheme:s=!0,storageKey:u="theme",themes:p=l,defaultTheme:m=r?"system":"light",attribute:y="data-theme",value:g,children:v,nonce:b})=>{let[x,P]=n.useState(()=>d(u,m)),[_,w]=n.useState(()=>d(u)),j=g?Object.values(g):p,R=n.useCallback(e=>{let n=e;if(!n)return;"system"===e&&r&&(n=h());let i=g?g[n]:n,a=t?f():null,l=document.documentElement;if("class"===y?(l.classList.remove(...j),i&&l.classList.add(i)):i?l.setAttribute(y,i):l.removeAttribute(y),s){let e=o.includes(m)?m:null,t=o.includes(n)?n:e;l.style.colorScheme=t}null==a||a()},[]),S=n.useCallback(e=>{let t="function"==typeof e?e(e):e;P(t);try{localStorage.setItem(u,t)}catch(e){}},[e]),E=n.useCallback(t=>{w(h(t)),"system"===x&&r&&!e&&R("system")},[x,e]);n.useEffect(()=>{let e=window.matchMedia(i);return e.addListener(E),E(e),()=>e.removeListener(E)},[E]),n.useEffect(()=>{let e=e=>{e.key===u&&S(e.newValue||m)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[S]),n.useEffect(()=>{R(null!=e?e:x)},[e,x]);let T=n.useMemo(()=>({theme:x,setTheme:S,forcedTheme:e,resolvedTheme:"system"===x?_:x,themes:r?[...p,"system"]:p,systemTheme:r?_:void 0}),[x,S,e,_,r,p]);return n.createElement(a.Provider,{value:T},n.createElement(c,{forcedTheme:e,disableTransitionOnChange:t,enableSystem:r,enableColorScheme:s,storageKey:u,themes:p,defaultTheme:m,attribute:y,value:g,children:v,attrs:j,nonce:b}),v)},c=n.memo(({forcedTheme:e,storageKey:t,attribute:r,enableSystem:a,enableColorScheme:s,defaultTheme:l,value:u,attrs:c,nonce:d})=>{let f="system"===l,h="class"===r?`var d=document.documentElement,c=d.classList;c.remove(${c.map(e=>`'${e}'`).join(",")});`:`var d=document.documentElement,n='${r}',s='setAttribute';`,p=s?(o.includes(l)?l:null)?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${l}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",m=(e,t=!1,n=!0)=>{let i=u?u[e]:e,a=t?e+"|| ''":`'${i}'`,l="";return s&&n&&!t&&o.includes(e)&&(l+=`d.style.colorScheme = '${e}';`),"class"===r?t||i?l+=`c.add(${a})`:l+="null":i&&(l+=`d[s](n,${a})`),l},y=e?`!function(){${h}${m(e)}}()`:a?`!function(){try{${h}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${f})){var t='${i}',m=window.matchMedia(t);if(m.media!==t||m.matches){${m("dark")}}else{${m("light")}}}else if(e){${u?`var x=${JSON.stringify(u)};`:""}${m(u?"x[e]":"e",!0)}}${f?"":"else{"+m(l,!1,!1)+"}"}${p}}catch(e){}}()`:`!function(){try{${h}var e=localStorage.getItem('${t}');if(e){${u?`var x=${JSON.stringify(u)};`:""}${m(u?"x[e]":"e",!0)}}else{${m(l,!1,!1)};}${p}}catch(t){}}();`;return n.createElement("script",{nonce:d,dangerouslySetInnerHTML:{__html:y}})}),d=(e,t)=>{},f=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},h=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")},1009:(e,t,r)=>{"use strict";r.d(t,{m6:()=>X});let n=e=>{let t=s(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||a(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),i=n?o(e.slice(1),n):void 0;if(i)return i;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},i=/^\[(.+)\]$/,a=e=>{if(i.test(e)){let t=i.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},s=e=>{let{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return d(Object.entries(e.classGroups),r).forEach(([e,r])=>{l(r,n,e,t)}),n},l=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e){if(c(e)){l(e(n),t,r,n);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,o])=>{l(o,u(t,e),r,n)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,i)=>{r.set(o,i),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},h=e=>{let{separator:t,experimentalParseClassName:r}=e,n=1===t.length,o=t[0],i=t.length,a=e=>{let r;let a=[],s=0,l=0;for(let u=0;u<e.length;u++){let c=e[u];if(0===s){if(c===o&&(n||e.slice(u,u+i)===t)){a.push(e.slice(l,u)),l=u+i;continue}if("/"===c){r=u;continue}}"["===c?s++:"]"===c&&s--}let u=0===a.length?e:e.substring(l),c=u.startsWith("!"),d=c?u.substring(1):u;return{modifiers:a,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};return r?e=>r({className:e,parseClassName:a}):a},p=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},m=e=>({cache:f(e.cacheSize),parseClassName:h(e),...n(e)}),y=/\s+/,g=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o}=t,i=[],a=e.trim().split(y),s="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{modifiers:l,hasImportantModifier:u,baseClassName:c,maybePostfixModifierPosition:d}=r(t),f=!!d,h=n(f?c.substring(0,d):c);if(!h){if(!f||!(h=n(c))){s=t+(s.length>0?" "+s:s);continue}f=!1}let m=p(l).join(":"),y=u?m+"!":m,g=y+h;if(i.includes(g))continue;i.push(g);let v=o(h,f);for(let e=0;e<v.length;++e){let t=v[e];i.push(y+t)}s=t+(s.length>0?" "+s:s)}return s};function v(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=b(e))&&(n&&(n+=" "),n+=t);return n}let b=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=b(e[n]))&&(r&&(r+=" "),r+=t);return r},x=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},P=/^\[(?:([a-z-]+):)?(.+)\]$/i,_=/^\d+\/\d+$/,w=new Set(["px","full","screen"]),j=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,R=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,E=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,T=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,O=e=>A(e)||w.has(e)||_.test(e),M=e=>W(e,"length",$),A=e=>!!e&&!Number.isNaN(Number(e)),C=e=>W(e,"number",A),D=e=>!!e&&Number.isInteger(Number(e)),k=e=>e.endsWith("%")&&A(e.slice(0,-1)),N=e=>P.test(e),L=e=>j.test(e),F=new Set(["length","size","percentage"]),U=e=>W(e,F,G),V=e=>W(e,"position",G),I=new Set(["image","url"]),B=e=>W(e,I,Y),z=e=>W(e,"",K),H=()=>!0,W=(e,t,r)=>{let n=P.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},$=e=>R.test(e)&&!S.test(e),G=()=>!1,K=e=>E.test(e),Y=e=>T.test(e);Symbol.toStringTag;let X=function(e,...t){let r,n,o;let i=function(s){return n=(r=m(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,i=a,a(s)};function a(e){let t=n(e);if(t)return t;let i=g(e,r);return o(e,i),i}return function(){return i(v.apply(null,arguments))}}(()=>{let e=x("colors"),t=x("spacing"),r=x("blur"),n=x("brightness"),o=x("borderColor"),i=x("borderRadius"),a=x("borderSpacing"),s=x("borderWidth"),l=x("contrast"),u=x("grayscale"),c=x("hueRotate"),d=x("invert"),f=x("gap"),h=x("gradientColorStops"),p=x("gradientColorStopPositions"),m=x("inset"),y=x("margin"),g=x("opacity"),v=x("padding"),b=x("saturate"),P=x("scale"),_=x("sepia"),w=x("skew"),j=x("space"),R=x("translate"),S=()=>["auto","contain","none"],E=()=>["auto","hidden","clip","visible","scroll"],T=()=>["auto",N,t],F=()=>[N,t],I=()=>["",O,M],W=()=>["auto",A,N],$=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],G=()=>["solid","dashed","dotted","double","none"],K=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Y=()=>["start","end","center","between","around","evenly","stretch"],X=()=>["","0",N],Z=()=>["auto","avoid","all","avoid-page","page","left","right","column"],q=()=>[A,N];return{cacheSize:500,separator:":",theme:{colors:[H],spacing:[O,M],blur:["none","",L,N],brightness:q(),borderColor:[e],borderRadius:["none","","full",L,N],borderSpacing:F(),borderWidth:I(),contrast:q(),grayscale:X(),hueRotate:q(),invert:X(),gap:F(),gradientColorStops:[e],gradientColorStopPositions:[k,M],inset:T(),margin:T(),opacity:q(),padding:F(),saturate:q(),scale:q(),sepia:X(),skew:q(),space:F(),translate:F()},classGroups:{aspect:[{aspect:["auto","square","video",N]}],container:["container"],columns:[{columns:[L]}],"break-after":[{"break-after":Z()}],"break-before":[{"break-before":Z()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...$(),N]}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:S()}],"overscroll-x":[{"overscroll-x":S()}],"overscroll-y":[{"overscroll-y":S()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",D,N]}],basis:[{basis:T()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",N]}],grow:[{grow:X()}],shrink:[{shrink:X()}],order:[{order:["first","last","none",D,N]}],"grid-cols":[{"grid-cols":[H]}],"col-start-end":[{col:["auto",{span:["full",D,N]},N]}],"col-start":[{"col-start":W()}],"col-end":[{"col-end":W()}],"grid-rows":[{"grid-rows":[H]}],"row-start-end":[{row:["auto",{span:[D,N]},N]}],"row-start":[{"row-start":W()}],"row-end":[{"row-end":W()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",N]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",N]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...Y()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...Y(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...Y(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[y]}],mx:[{mx:[y]}],my:[{my:[y]}],ms:[{ms:[y]}],me:[{me:[y]}],mt:[{mt:[y]}],mr:[{mr:[y]}],mb:[{mb:[y]}],ml:[{ml:[y]}],"space-x":[{"space-x":[j]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[j]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",N,t]}],"min-w":[{"min-w":[N,t,"min","max","fit"]}],"max-w":[{"max-w":[N,t,"none","full","min","max","fit","prose",{screen:[L]},L]}],h:[{h:[N,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[N,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[N,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[N,t,"auto","min","max","fit"]}],"font-size":[{text:["base",L,M]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",C]}],"font-family":[{font:[H]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",N]}],"line-clamp":[{"line-clamp":["none",A,C]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",O,N]}],"list-image":[{"list-image":["none",N]}],"list-style-type":[{list:["none","disc","decimal",N]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...G(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",O,M]}],"underline-offset":[{"underline-offset":["auto",O,N]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:F()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...$(),V]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",U]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},B]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[p]}],"gradient-via-pos":[{via:[p]}],"gradient-to-pos":[{to:[p]}],"gradient-from":[{from:[h]}],"gradient-via":[{via:[h]}],"gradient-to":[{to:[h]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...G(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:G()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...G()]}],"outline-offset":[{"outline-offset":[O,N]}],"outline-w":[{outline:[O,M]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[O,M]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",L,z]}],"shadow-color":[{shadow:[H]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...K(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":K()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",L,N]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[b]}],sepia:[{sepia:[_]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[_]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",N]}],duration:[{duration:q()}],ease:[{ease:["linear","in","out","in-out",N]}],delay:[{delay:q()}],animate:[{animate:["none","spin","ping","pulse","bounce",N]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[P]}],"scale-x":[{"scale-x":[P]}],"scale-y":[{"scale-y":[P]}],rotate:[{rotate:[D,N]}],"translate-x":[{"translate-x":[R]}],"translate-y":[{"translate-y":[R]}],"skew-x":[{"skew-x":[w]}],"skew-y":[{"skew-y":[w]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",N]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",N]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":F()}],"scroll-mx":[{"scroll-mx":F()}],"scroll-my":[{"scroll-my":F()}],"scroll-ms":[{"scroll-ms":F()}],"scroll-me":[{"scroll-me":F()}],"scroll-mt":[{"scroll-mt":F()}],"scroll-mr":[{"scroll-mr":F()}],"scroll-mb":[{"scroll-mb":F()}],"scroll-ml":[{"scroll-ml":F()}],"scroll-p":[{"scroll-p":F()}],"scroll-px":[{"scroll-px":F()}],"scroll-py":[{"scroll-py":F()}],"scroll-ps":[{"scroll-ps":F()}],"scroll-pe":[{"scroll-pe":F()}],"scroll-pt":[{"scroll-pt":F()}],"scroll-pr":[{"scroll-pr":F()}],"scroll-pb":[{"scroll-pb":F()}],"scroll-pl":[{"scroll-pl":F()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",N]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[O,M,C]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},3370:(e,t,r)=>{"use strict";function n(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>n,_interop_require_default:()=>n})}};