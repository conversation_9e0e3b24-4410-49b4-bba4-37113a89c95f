{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "6fd6b437171f7aaa2115f55fafe71150", "previewModeSigningKey": "f278936f47eefdaf9b8ff8aac431f370a7fdae2ba1ff5dfccf750c5209c81efc", "previewModeEncryptionKey": "caa0646c0f7dd6bd4ccc48be9dd80092419e1933cdcc958c77d1c76df40c57ba"}}