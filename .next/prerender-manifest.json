{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "79cdbfc938594ee77716a456e5390900", "previewModeSigningKey": "e1fd28bf2b0bba0c43d5ad9b56ef4b989a1e8c155a9939c0dfe1a9bae1d87323", "previewModeEncryptionKey": "76a7220b2da0c4c288b0900e2d013a12ab43857f8a71d793a7f6434133cc7098"}}