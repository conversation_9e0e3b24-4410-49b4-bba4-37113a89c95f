{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "268a84967413761aece86e5981f94300", "previewModeSigningKey": "952be66eec015287456dbe5517bbdd5b3f65be1b41697cc00c15fb8f677ec698", "previewModeEncryptionKey": "7a5438a343a6986a2fd2315da856be2299465a72debc11446f64460b0fde0bd4"}}