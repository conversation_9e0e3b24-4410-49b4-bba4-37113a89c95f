'use strict';

module.exports = {
    moduleType: 'locale',
    name: 'si',
    dictionary: {
        'Autoscale': 'ස්වයං පරිමාණනය',                                                                                      // components/modebar/buttons.js:208
        'Box Select': 'කොටුව වරණය',                                                                                      // components/modebar/buttons.js:116
        'Click to enter Colorscale title': 'වර්ණපරිමාණන සිරැසිය ඇතුල් කිරීමට ඔබන්න',                                          // plots/plots.js:322
        'Click to enter Component A title': 'සංරචකයේ අ සිරැසිය ඇතුල් කිරීමට ඔබන්න',                                           // plots/ternary/ternary.js:372
        'Click to enter Component B title': 'සංරචකයේ ආ සිරැසිය ඇතුල් කිරීමට ඔබන්න',                                          // plots/ternary/ternary.js:382
        'Click to enter Component C title': 'සංරචකයේ ඇ සිරැසිය ඇතුල් කිරීමට ඔබන්න',                                         // plots/ternary/ternary.js:392
        'Click to enter Plot title': 'කොටුවෙහි සිරැසිය ඇතුල් කිරීමට ඔබන්න',                                                  // plots/plots.js:319
        'Click to enter X axis title': 'තිරස් අක්‍ෂයේ සිරැසිය ඇතුල් කිරීමට ඔබන්න',                                             // plots/plots.js:320
        'Click to enter Y axis title': 'සිරස් අක්‍ෂයේ සිරැසිය ඇතුල් කිරීමට ඔබන්න',                                            // plots/plots.js:321
        'Click to enter radial axis title': 'අරීය අක්‍ෂයේ සිරැසිය ඇතුල් කිරීමට ඔබන්න',                                      // plots/polar/polar.js:575
        'Compare data on hover': 'සුනංගු කිරීමේදී දත්ත සසඳන්න',                                                           // components/modebar/buttons.js:239
        'Double-click on legend to isolate one trace': 'Double-click on legend to isolate one trace',                // components/legend/handle_click.js:20
        'Double-click to zoom back out': 'ආපසු කුඩාලනය කිරීමට දෙවරක් ඔබන්න',                                          // plots/cartesian/dragbox.js:1166
        'Download plot': 'කොටුව බාගන්න',                                                                             // components/modebar/buttons.js:45
        'Download plot as a png': 'කොටුව පීඑන්ජී ලෙස බාගන්න',                                                          // components/modebar/buttons.js:44
        'Draw circle': 'කවයක් අඳින්න',                                                                                 // components/modebar/buttons.js:171
        'Draw closed freeform': 'සංවෘත ලෙස නිදහසේ අඳින්න',                                                              // components/modebar/buttons.js:135
        'Draw line': 'රේඛාවක් අඳින්න',                                                                                   // components/modebar/buttons.js:153
        'Draw open freeform': 'විවෘත ලෙස නිදහසේ අඳින්න',                                                                 // components/modebar/buttons.js:144
        'Draw rectangle': 'සෘජුකෝණයක් අඳින්න',                                                                           // components/modebar/buttons.js:162
        'Edit in Chart Studio': 'ප්‍රස්තාර චිත්‍රාගාරයෙහි සංස්කරණය',                                                             // components/modebar/buttons.js:77
        'Erase active shape': 'සක්‍රිය හැඩය මකන්න',                                                                         // components/modebar/buttons.js:180
        'IE only supports svg.  Changing format to svg.': 'ඉ.එ. එස්වීජී සඳහා පමණක් සහාය දක්වයි. ආකෘතිය එයට වෙනස් වෙමින්',    // components/modebar/buttons.js:55
        'Lasso Select': 'මායිම වරණය',                                                                                    // components/modebar/buttons.js:126
        'Orbital rotation': 'කාක්‍ෂික කරකැවීම',                                                                            // components/modebar/buttons.js:342
        'Pan': 'චලකය',                                                                                                 // components/modebar/buttons.js:106
        'Produced with Plotly': 'ප්ලොට්ලි සමඟ උපදවන ලදි',                                                               // components/modebar/modebar.js:295
        'Reset': 'යළි සකසන්න',                                                                                        // components/modebar/buttons.js:515
        'Reset axes': 'අක්‍ෂ යළි සකසන්න',                                                                             // components/modebar/buttons.js:218
        'Reset camera to default': 'Reset camera to default',                                                       // components/modebar/buttons.js:381
        'Reset camera to last save': 'Reset camera to last save',                                                   // components/modebar/buttons.js:390
        'Reset view': 'දැක්ම යළි සකසන්න',                                                                            // components/modebar/buttons.js:599
        'Reset views': 'දැක්ම් යළි සකසන්න',                                                                           // components/modebar/buttons.js:637
        'Show closest data on hover': 'සුනංගු කිරීමේදී ආසන්නම දත්ත පෙන්වන්න',                                            // components/modebar/buttons.js:228
        'Snapshot succeeded': 'ඡායාරූපය සාර්ථකයි',                                                                      // components/modebar/buttons.js:67
        'Sorry, there was a problem downloading your snapshot!': 'සමාවන්න, ඔබගේ ඡායාරූපය බාගැනීමේ ගැටලුවක් ඇත!',       // components/modebar/buttons.js:70
        'Taking snapshot - this may take a few seconds': 'ඡායාරූපය ගැනෙමින් - මෙයට තත්. කිහිපයක් ගතවිය හැකිය',            // components/modebar/buttons.js:52
        'Toggle Spike Lines': 'Toggle Spike Lines',                                                                     // components/modebar/buttons.js:656
        'Toggle show closest data on hover': 'Toggle show closest data on hover',                                       // components/modebar/buttons.js:440
        'Turntable rotation': 'බමර කරකැවීම',                                                                            // components/modebar/buttons.js:351
        'Zoom': 'විශාල කරන්න',                                                                                           // components/modebar/buttons.js:96
        'Zoom in': 'විශාලනය',                                                                                            // components/modebar/buttons.js:188
        'Zoom out': 'කුඩාලනය',                                                                                           // components/modebar/buttons.js:198
        'close:': 'වසන්න:',                                                                                              // traces/ohlc/calc.js:108
        'concentration:': 'සංකේන්ද්‍රණය:',                                                                                  // traces/sankey/plot.js:160
        'high:': 'high:',                                                                                                // traces/ohlc/calc.js:106
        'incoming flow count:': 'ඇතුළටඑන ප්‍රවාහය ගණනය:',                                                                // traces/sankey/plot.js:161
        'kde:': 'kde:',                                                                                                   // traces/violin/calc.js:86
        'lat:': 'lat:',                                                                                                    // traces/densitymapbox/calc.js:42
        'lon:': 'lon:',                                                                                                     // traces/densitymapbox/calc.js:43
        'low:': 'low:',                                                                                                    // traces/ohlc/calc.js:107
        'lower fence:': 'lower fence:',                                                                                   // traces/box/calc.js:290
        'max:': 'උපරිම:',                                                                                                 // traces/box/calc.js:288
        'mean ± σ:': 'මධ්‍යන්‍ය ± σ:',                                                                                       // traces/box/calc.js:289
        'mean:': 'මධ්‍යන්‍ය:',                                                                                               // traces/box/calc.js:289
        'median:': 'මධ්‍යසථය:',                                                                                             // traces/box/calc.js:284
        'min:': 'අවම:',                                                                                                    // traces/box/calc.js:285
        'new text': 'නව පාඨය',                                                                                             // plots/plots.js:323
        'open:': 'විවෘත:',                                                                                                   // traces/ohlc/calc.js:105
        'outgoing flow count:': 'පිටතටයන ප්‍රවාහය ගණනය:',                                                                    // traces/sankey/plot.js:162
        'q1:': 'q1:',                                                                                                        // traces/box/calc.js:286
        'q3:': 'q3:',                                                                                                        // traces/box/calc.js:287
        'source:': 'මූලාශ්‍රය:',                                                                                                // traces/sankey/plot.js:158
        'target:': 'ඉලක්කය:',                                                                                              // traces/sankey/plot.js:159
        'trace': 'trace',                                                                                                  // plots/plots.js:325
        'upper fence:': 'upper fence:',                                                                                   // traces/box/calc.js:291
    },
    format: {
        days: ['ඉරිදා', 'සඳුදා', 'අඟහරුවාදා', 'බදාදා', 'බ්‍රහස්පතින්දා', 'සිකුරාදා', 'සෙනසුරාදා'],
        shortDays: ['ඉරිදා', 'සඳුදා', 'අඟහ', 'බදාදා', 'බ්‍රහස්', 'සිකු', 'සෙන'],
        months: [
            'දුරුතු', 'නවම්', 'මැදින්', 'බක්', 'වෙසක්', 'පොසොන්',
            'ඇසළ', 'නිකිණි', 'බිනර', 'වප්', 'ඉල්', 'උඳුවප්'
        ],
        shortMonths: [
            'දුරුතු', 'නවම්', 'මැදින්', 'බක්', 'වෙසක්', 'පොසොන්',
            'ඇසළ', 'නිකිණි', 'බිනර', 'වප්', 'ඉල්', 'උඳු'
        ],
        date: '%d-%m-%Y'
    }
};
