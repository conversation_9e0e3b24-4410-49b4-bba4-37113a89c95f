'use strict';

module.exports = {
    moduleType: 'locale',
    name: 'uk',
    dictionary: {
        'Autoscale': 'Автоматичне шкалювання',
        'Box Select': 'Виділення прямокутної області',
        'Click to enter Colorscale title': 'Натисніть для введення назви шкали кольору',
        'Click to enter Component A title': 'Натисніть для введення назви компоненту A',
        'Click to enter Component B title': 'Натисніть для введення назви компоненту B',
        'Click to enter Component C title': 'Натисніть для введення назви компоненту C',
        'Click to enter Plot title': 'Натисніть для введення назви графіка',
        'Click to enter X axis title': 'Натисніть для введення назви осі X',
        'Click to enter Y axis title': 'Натисніть для введення назви осі Y',
        'Click to enter radial axis title': 'Натисніть для введення назви полярної осі',
        'Compare data on hover': 'При наведенні показувати всі дані',
        'Double-click on legend to isolate one trace': 'Двічі клацніть по легенді для виділення окремих даних',
        'Double-click to zoom back out': 'Для встановлення масштабу значення за замовчуванням двічі клацніть мишею',
        'Download plot': 'Зберегти графік',
        'Download plot as a png': 'Зберегти у форматі PNG',
        'Edit in Chart Studio': 'Редагувати у Chart Studio',
        'IE only supports svg.  Changing format to svg.': 'IE підтримує лише svg. Формат змінюється на svg.',
        'Lasso Select': 'Ласо',
        'Orbital rotation': 'Рух по орбіті',
        'Pan': 'Зсув',
        'Produced with Plotly.js': 'Створено за допомогою Plotly.js',
        'Reset': 'Встановити значення за замовчуванням',
        'Reset axes': 'Встановити осям значення за замовчуванням',
        'Reset camera to default': 'Встановити камері значення за замовчуванням',
        'Reset camera to last save': 'Повернути камеру в останній збережений стан',
        'Reset view': 'Встановити відображенню значення за замовчуванням',
        'Reset views': 'Встановити відображенням значення за замовчуванням',
        'Show closest data on hover': 'При наведенні показувати найближчі дані',
        'Snapshot succeeded': 'Знімок успішно створений',
        'Sorry, there was a problem downloading your snapshot!': 'На жаль, виникла проблема при збереженні знімку',
        'Taking snapshot - this may take a few seconds': 'Створюється знімок - це може зайняти кілька секунд',
        'Toggle Spike Lines': 'Увімкнути/вимкнути відображення ліній проекцій точок',
        'Toggle show closest data on hover': 'Увімкнути/вимкнути відображення найближчих даних при наведенні',
        'Turntable rotation': 'Обертання на поворотному столі',
        'Zoom': 'Зум',
        'Zoom in': 'Збільшити',
        'Zoom out': 'Зменшити',
        'close:': 'Закриття:',
        'concentration:': 'Концентрація:',
        'high:': 'Максимум:',
        'incoming flow count:': 'Кількість вхідних зв\'язків:',
        'kde:': 'Ядрова оцінка густини розподілу:',
        'lat:': 'Широта:',
        'lon:': 'Довгота:',
        'low:': 'Мінімум:',
        'lower fence:': 'Нижня границя:',
        'max:': 'Макс.:',
        'mean ± σ:': 'Середнє ± σ:',
        'mean:': 'Середнє:',
        'median:': 'Медіана:',
        'min:': 'Мін.:',
        'new text': 'Новий текст',
        'open:': 'Відкриття:',
        'outgoing flow count:': 'Кількість вихідних зв\'язків:',
        'q1:': 'q1:',
        'q3:': 'q3:',
        'source:': 'Джерело:',
        'target:': 'Ціль:',
        'trace': 'Ряд',
        'upper fence:': 'Верхня границя:'
    },
    format: {
        days: [
            'неділя', 'понеділок', 'вівторок', 'середа',
            'четвер', 'п\'ятниця', 'субота'
        ],
        shortDays: ['нд', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],
        months: [
            'Січень', 'Лютий', 'Березень', 'Квітень', 'Травень', 'Червень',
            'Липень', 'Серпень', 'Вересень', 'Жовтень', 'Листопад', 'Грудень'
        ],
        shortMonths: [
            'Січ.', 'Лют.', 'Берез.', 'Квіт.', 'Трав.', 'Черв.',
            'Лип.', 'Серп.', 'Верес.', 'Жовт.', 'Листоп.', 'Груд.'
        ],
        date: '%d.%m.%Y',
        decimal: ',',
        thousands: ' '
    }
};
