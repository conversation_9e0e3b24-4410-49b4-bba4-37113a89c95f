'use strict';

module.exports = {
    moduleType: 'locale',
    name: 'zh-TW',
    dictionary: {
        'Autoscale': '自動縮放',                                                                         // components/modebar/buttons.js:139
        'Box Select': '矩形選擇',                                                                          // components/modebar/buttons.js:103
        'Click to enter Colorscale title': '點擊以輸入色階標題',                     // plots/plots.js:437
        'Click to enter Component A title': '點擊以輸入元件 A 標題',               // plots/ternary/ternary.js:386
        'Click to enter Component B title': '點擊以輸入元件 B 標題',               // plots/ternary/ternary.js:400
        'Click to enter Component C title': '點擊以輸入元件 C 標題',               // plots/ternary/ternary.js:411
        'Click to enter Plot title': '點擊以輸入繪圖標題',                             // plot_api/plot_api.js:579
        'Click to enter X axis title': '點擊以輸入 X 軸標題',                                // plots/plots.js:435
        'Click to enter Y axis title': '點擊以輸入 Y 軸標題',                                // plots/plots.js:436
        'Click to enter radial axis title': '點擊以輸入輻射軸標題',                       // plots/polar/polar.js:575
        'Compare data on hover': '游標停留時比較資料',                                                 // components/modebar/buttons.js:167
        'Double-click on legend to isolate one trace': '雙擊圖例以隔離單一軌跡', // components/legend/handle_click.js:90
        'Double-click to zoom back out': '雙擊回復縮放',                               // plots/cartesian/dragbox.js:299
        'Download plot as a png': '下載圖表為 PNG 圖檔',                                             // components/modebar/buttons.js:52
        'Download plot': '下載圖表',                                                                  // components/modebar/buttons.js:53
        'Draw circle': '繪製圓圈',                                            // components/modebar/buttons.js:171
        'Draw closed freeform': '繪製封閉的任意圖形',                                            // components/modebar/buttons.js:135
        'Draw line': '繪製線條',                                            // components/modebar/buttons.js:153
        'Draw open freeform': '繪製開放的任意圖形',                                            // components/modebar/buttons.js:144
        'Draw rectangle': '繪製矩形',                                            // components/modebar/buttons.js:162
        'Edit in Chart Studio': '於 Chart Studio 編輯',                                                      // components/modebar/buttons.js:76
        'Erase active shape': '清除作用中的形狀',                                            // components/modebar/buttons.js:180
        'IE only supports svg.  Changing format to svg.': 'IE 僅支援 SVG，將變更格式為 SVG。', // components/modebar/buttons.js:60
        'Lasso Select': '套索選擇',                                                                      // components/modebar/buttons.js:112
        'Orbital rotation': '軌道旋轉',                                                                    // components/modebar/buttons.js:279
        'Pan': '平移',                                                                                     // components/modebar/buttons.js:94
        'Produced with Plotly.js': '使用 Plotly.js 製作',                                                  // components/modebar/modebar.js:256
        'Reset': '重置',                                                                                // components/modebar/buttons.js:432
        'Reset axes': '重置軸',                                                                       // components/modebar/buttons.js:148
        'Reset camera to default': '重置相機至預設位置',                                              // components/modebar/buttons.js:314
        'Reset camera to last save': '重置相機至上次儲存的位置',                                        // components/modebar/buttons.js:322
        'Reset view': '重置視圖',                                                                   // components/modebar/buttons.js:583
        'Reset views': '重置視圖',                                                                  // components/modebar/buttons.js:529
        'Show closest data on hover': '游標停留時顯示最接近的資料',                                     // components/modebar/buttons.js:157
        'Snapshot succeeded': '快照成功',                                                     // components/modebar/buttons.js:66
        'Sorry, there was a problem downloading your snapshot!': '抱歉，下載快照時發生錯誤!', // components/modebar/buttons.js:69
        'Taking snapshot - this may take a few seconds': '產生快照中 - 可能需要一點時間',                          // components/modebar/buttons.js:57
        'Zoom': '縮放',                                                                                   // components/modebar/buttons.js:85
        'Zoom in': '放大',                                                                                  // components/modebar/buttons.js:121
        'Zoom out': '縮小',                                                                                 // components/modebar/buttons.js:130
        'close:': '關閉:',                                                                                // traces/ohlc/transform.js:139
        'concentration': '集中',                                         // traces/sankey/plot.js:160
        'trace': '軌跡:',                                                                               // plots/plots.js:439
        'lat:': '緯度:',                                                                                    // traces/scattergeo/calc.js:48
        'lon:': '經度:',                                                                                    // traces/scattergeo/calc.js:49
        'q1:': '第一四分位數:',                                                                                       // traces/box/calc.js:130
        'q3:': '第三四分位數:',                                                                                       // traces/box/calc.js:131
        'source:': '來源:',                                                                               // traces/sankey/plot.js:140
        'target:': '目標:',                                                                            // traces/sankey/plot.js:141
        'lower fence:': '下圍籬值:',                                                                     // traces/box/calc.js:134
        'upper fence:': '上圍籬值:',                                                                     // traces/box/calc.js:135
        'max:': '最大值:',                                                                                    // traces/box/calc.js:132
        'mean ± σ:': '平均 ± σ:',                                                                       // traces/box/calc.js:133
        'mean:': '平均值:',                                                                                   // traces/box/calc.js:133
        'median:': '中位數:',                                                                               // traces/box/calc.js:128
        'min:': '最小值:',                                                                                  // traces/box/calc.js:129
        'Turntable rotation': '轉盤旋轉:',                                                                    // components/modebar/buttons.js:288
        'Toggle Spike Lines': '切換尖峰線',                                                   // components/modebar/buttons.js:548
        'open:': '開啟:',                                                                                   // traces/ohlc/transform.js:136
        'high:': '高:',                                                                                     // traces/ohlc/transform.js:137
        'low:': '低:',                                                                                      // traces/ohlc/transform.js:138
        'Toggle show closest data on hover': '切換滑鼠懸停顯示最接近的資料',                          // components/modebar/buttons.js:353
        'incoming flow count:': '傳入流量計數:',                                                      // traces/sankey/plot.js:142
        'outgoing flow count:': '傳出流量計數:',                                                      // traces/sankey/plot.js:143
        'kde:': 'kde:',                                                                                     // traces/violin/calc.js:73
        'new text': '新文本'
    },
    format: {
        days: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
        shortDays: ['週日', '週一', '週二', '週三', '週四', '週五', '週六'],
        months: [
            '一月', '二月', '三月', '四月', '五月', '六月',
            '七月', '八月', '九月', '十月', '十一月', '十二月'
        ],
        shortMonths: [
            '1月', '2月', '3月', '4月', '5月', '6月',
            '7月', '8月', '9月', '10月', '11月', '12月'
        ],
        date: '%Y-%m-%d'
    }
};
