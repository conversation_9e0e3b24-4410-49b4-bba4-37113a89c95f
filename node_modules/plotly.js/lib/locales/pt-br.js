'use strict';

module.exports = {
    moduleType: 'locale',
    name: 'pt-BR',
    dictionary: {
        'Autoscale': 'Escala automática',
        'Box Select': 'Seleção retangular',
        'Click to enter Colorscale title': 'Clique para editar o título da escala de cor',
        'Click to enter Component A title': 'Clique para editar o título do Componente A',
        'Click to enter Component B title': 'Clique para editar o título do Componente B',
        'Click to enter Component C title': 'Clique para editar o título do Componente C',
        'Click to enter Plot title': 'Clique para editar o título do Gráfico',
        'Click to enter X axis title': 'Clique para editar o título do eixo X',
        'Click to enter Y axis title': 'Clique para editar o título do eixo Y',
        'Click to enter radial axis title': 'Clique para editar o título do eixo radial',
        'Compare data on hover': 'Comparar dados ao pairar',
        'Double-click on legend to isolate one trace': 'Duplo clique na legenda para isolar uma série',
        'Double-click to zoom back out': 'Duplo clique para reverter zoom',
        'Download plot as a png': 'Fazer download do gráfico como imagem (png)',
        'Download plot': 'Fazer download do gráfico',
        'Edit in Chart Studio': 'Editar no Chart Studio',
        'IE only supports svg.  Changing format to svg.': 'IE suporta apenas svg. Alterando formato para svg',
        'Lasso Select': 'Seleção de laço',
        'Orbital rotation': 'Rotação orbital',
        'Pan': 'Mover',
        'Produced with Plotly.js': 'Criado com o Plotly.js',
        'Reset': 'Restaurar',
        'Reset axes': 'Restaurar eixos',
        'Reset camera to default': 'Restaurar câmera para padrão',
        'Reset camera to last save': 'Restaurar câmera para última salva',
        'Reset view': 'Restaurar visão',
        'Reset views': 'Restaurar visões',
        'Show closest data on hover': 'Exibir dado mais próximo ao pairar',
        'Snapshot succeeded': 'Captura instantânea completa',
        'Sorry, there was a problem downloading your snapshot!': 'Desculpe, houve um problema no download de sua captura instantânea!',
        'Taking snapshot - this may take a few seconds': 'Efetuando captura instantânea - isso pode levar alguns instantes',
        'Toggle Spike Lines': 'Habilitar/desabilitar triangulação de linhas',
        'Toggle show closest data on hover': 'Habilitar/desabilitar exibição de dado mais próximo ao pairar',
        'Turntable rotation': 'Rotação de mesa',
        'Zoom': 'Zoom',
        'Zoom in': 'Ampliar zoom',
        'Zoom out': 'Reduzir zoom',
        'close': 'fechamento',
        'high': 'alta',
        'incoming flow count': 'contagem de fluxo de entrada',
        'kde': 'kde',
        'lat': 'latitude',
        'lon': 'longitude',
        'low': 'baixa',
        'lower fence': 'limite inferior',
        'max': 'máximo',
        'mean ± σ': 'média ± σ',
        'mean': 'média',
        'median': 'mediana',
        'min': 'mínimo',
        'new text': 'novo texto',
        'open': 'abertura',
        'outgoing flow count': 'contagem de fluxo de saída',
        'q1': 'q1',
        'q3': 'q3',
        'source': 'origem',
        'target': 'destino',
        'trace': 'série',
        'upper fence': 'limite superior'
    },
    format: {
        days: [
            'Domingo', 'Segunda-feira', 'Terça-feira', 'Quarta-feira',
            'Quinta-feira', 'Sexta-feira', 'Sábado'
        ],
        shortDays: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'],
        months: [
            'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
            'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
        ],
        shortMonths: [
            'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
            'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
        ],
        date: '%d/%m/%Y',
        decimal: ',',
        thousands: '.'
    }
};
