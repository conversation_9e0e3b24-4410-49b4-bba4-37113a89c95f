Thanks for your interest in plotly.js!

Before opening an issue, please search for existing and closed issues. If your problem or idea is not addressed yet, [please open a new issue](https://github.com/plotly/plotly.js/issues/new).

Bug reports **must** be accompanied with a reproducible example. We recommend using [codepen](http://codepen.io/), [jsfiddle](https://jsfiddle.net/) or [jsbin](https://jsbin.com) to share your example. Please use the [latest un-minified version](https://cdn.plot.ly/plotly-latest.js) of plotly.js in your report unless not applicable.

If you don't know JavaScript and still want to help us by reporting a bug, please attach the `"data"` and `"layout"` attributes that describe your graph and updates (if required to detect the bug). One way to retrieve your graph's data and layout attributes is by exporting your graph to [Plotly Cloud](http://plot.ly/). To do so, click on the _Edit in Chart Studio_ mode bar button (the 2nd one from the left by default) and follow these [instructions](https://help.plot.ly/save-share-and-export-in-plotly/), or watch this [screencast](https://community.plot.ly/t/mega-sharing-graphs-with-chart-studio/8869).

Issues found on the example pages from https://plot.ly/javascript/ should be filed in our [documentation repo](https://github.com/plotly/documentation/issues) with the exception of https://plot.ly/javascript/reference which should be filed here.

Note that GitHub issues are reserved for bug reports and feature requests only. Implementation questions should be asked on community.plot.ly (tagged [`plotly-js`](http://community.plot.ly/c/plotly-js)) or on Stack Overflow (tagged [`plotly`](https://stackoverflow.com/questions/tagged/plotly)).

Comments on GitHub issues or pull requests should add content to the discussions. Approbation comments such as *+1* or *I would like this feature to be implemented as well* will be deleted by the maintainers. Please use [GitHub reactions](https://github.com/blog/2119-add-reactions-to-pull-requests-issues-and-comments) instead.
