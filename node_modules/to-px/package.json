{"name": "to-px", "version": "1.0.1", "description": "Convert any CSS unit to logical pixels (\"px\")", "main": "topx.js", "directories": {"test": "test"}, "dependencies": {"parse-unit": "^1.0.1"}, "devDependencies": {"almost-equal": "^0.0.0", "tape": "^3.5.0"}, "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/to-px.git"}, "keywords": ["css", "px", "em", "ex", "line", "height", "ch", "rem", "vh", "vw", "vmin", "vmax", "unit", "conversion", "scale", "factor"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/to-px/issues"}, "homepage": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/to-px", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}}