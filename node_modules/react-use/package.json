{"name": "react-use", "version": "17.6.0", "description": "Collection of React Hooks", "main": "lib/index.js", "module": "esm/index.js", "sideEffects": false, "files": ["lib/", "esm/"], "types": "lib/index.d.ts", "typings": "lib/index.d.ts", "scripts": {"start": "yarn storybook", "test": "jest --maxWorkers 2", "test:ssr": "jest --maxWor<PERSON> 2 --config ./jest.config.node.ts", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint \"{src,tests,stories}/**/*.{ts,tsx}\"", "lint:fix": "yarn lint --fix", "lint:types": "tsc --noEmit", "build:cjs": "tsc", "build:es": "tsc -m esNext --outDir esm", "build": "yarn build:cjs && yarn build:es", "clean": "rim<PERSON>f lib storybook-static esm", "storybook": "start-storybook -p 6008", "storybook:build": "build-storybook", "storybook:upload": "gh-pages -d storybook-static --git \"$(which git)\"", "storybook:clean": "<PERSON><PERSON><PERSON> storybook-static", "release": "semantic-release"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "yarn lint && yarn clean && yarn build && yarn test"}}, "author": "@streamich", "license": "Unlicense", "repository": {"type": "git", "url": "https://github.com/streamich/react-use"}, "bugs": {"url": "https://github.com/streamich/react-use/issues"}, "homepage": "https://github.com/streamich/react-use#readme", "dependencies": {"@types/js-cookie": "^2.2.6", "@xobotyi/scrollbar-width": "^1.9.5", "copy-to-clipboard": "^3.3.1", "fast-deep-equal": "^3.1.3", "fast-shallow-equal": "^1.0.0", "js-cookie": "^2.2.1", "nano-css": "^5.6.2", "react-universal-interface": "^0.6.2", "resize-observer-polyfill": "^1.5.1", "screenfull": "^5.1.0", "set-harmonic-interval": "^1.0.1", "throttle-debounce": "^3.0.1", "ts-easing": "^0.2.0", "tslib": "^2.1.0"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "devDependencies": {"@babel/core": "7.24.9", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/preset-env": "7.24.8", "@babel/preset-react": "7.24.7", "@babel/preset-typescript": "7.24.7", "@semantic-release/changelog": "6.0.3", "@semantic-release/git": "10.0.1", "@semantic-release/npm": "12.0.1", "@shopify/jest-dom-mocks": "2.11.7", "@storybook/addon-actions": "6.4.9", "@storybook/addon-knobs": "6.4.0", "@storybook/addon-notes": "5.3.21", "@storybook/addon-options": "5.3.21", "@storybook/react": "6.4.9", "@testing-library/react": "12.1.2", "@testing-library/react-hooks": "7.0.2", "@types/jest": "27.5.2", "@types/react": "17.0.0", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "babel-core": "6.26.3", "babel-eslint": "10.1.0", "babel-loader": "8.3.0", "babel-plugin-dynamic-import-node": "2.3.3", "eslint": "8.57.0", "eslint-config-prettier": "8.10.0", "eslint-config-react-app": "6.0.0", "eslint-plugin-flowtype": "8.0.3", "eslint-plugin-import": "2.29.1", "eslint-plugin-jsx-a11y": "6.9.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "4.6.0", "fork-ts-checker-webpack-plugin": "6.5.3", "gh-pages": "5.0.0", "husky": "7.0.4", "jest": "26.6.3", "jest-localstorage-mock": "2.4.26", "keyboardjs": "2.7.0", "lint-staged": "12.5.0", "markdown-loader": "6.0.0", "prettier": "2.3.0", "raf-stub": "3.0.0", "react": "17.0.2", "react-dom": "17.0.2", "react-frame-component": "5.2.7", "react-spring": "9.7.4", "react-test-renderer": "17.0.2", "rebound": "0.1.0", "redux-logger": "3.0.6", "redux-thunk": "2.4.2", "rimraf": "3.0.2", "rxjs": "7.8.1", "semantic-release": "24.0.0", "ts-jest": "26.5.6", "ts-loader": "8.4.0", "ts-node": "10.9.2", "typescript": "4.1.5"}, "config": {"commitizen": {"path": "git-cz"}}, "release": {"branches": ["master", {"name": "next", "prerelease": "rc"}], "verifyConditions": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"], "prepare": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"]}, "lint-staged": {"src/**/**/*.{ts,tsx}": ["eslint --fix", "git add"]}, "volta": {"node": "14.21.3", "yarn": "1.22.22"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/react-use"}}