
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const consultationData = await request.json()

    // Log consultation request (in production, save to database)
    console.log('New consultation request:', {
      name: consultationData.name,
      email: consultationData.email,
      phone: consultationData.phone,
      language: consultationData.language,
      appointmentTime: consultationData.appointmentTime,
      timestamp: new Date().toISOString()
    })

    // Here you would typically save to database
    // For now, we'll just log it and return success

    return NextResponse.json({
      success: true,
      message: 'Consultation request received successfully'
    })

  } catch (error) {
    console.error('Consultation API error:', error)

    return NextResponse.json(
      { error: 'Failed to process consultation request' },
      { status: 500 }
    )
  }
}
