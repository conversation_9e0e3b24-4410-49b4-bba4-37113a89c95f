
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const contactData = await request.json()

    const {
      name,
      email,
      phone,
      subject,
      message,
      preferredContact
    } = contactData

    // Validate required fields
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Create contact submission record
    const contact = {
      id: Date.now().toString(),
      name,
      email,
      phone: phone || '',
      subject,
      message,
      preferredContact: preferredContact || 'email',
      status: 'new',
      createdAt: new Date().toISOString(),
      formType: 'contact'
    }

    // In a real application, you would:
    // 1. Save to database
    // 2. Send confirmation email to client
    // 3. Send notification email to Emanuel
    // 4. Create a ticket in support system

    console.log('New contact submission received:', contact)

    // Send automated responses (in a real app)
    try {
      // Client confirmation email
      const clientEmailData = {
        to: email,
        subject: 'Message Received - Emanuel <PERSON>ra Financial Services',
        body: `Dear ${name},

Thank you for contacting Emanuel Ibarra Financial Services!

**Your Message:**
Subject: ${subject}
Message: ${message}
Preferred Contact Method: ${preferredContact || 'Email'}

We have received your inquiry and <PERSON> will personally respond within 24 hours during business days.

**Emanuel's Contact Information:**
- Phone: (************* (Mon-Fri 9AM-6PM CST)
- Email: <EMAIL>
- Location: Fort Worth, TX

**Business Hours:**
- Monday - Friday: 9:00 AM - 6:00 PM
- Saturday: 10:00 AM - 2:00 PM  
- Sunday: By Appointment

For urgent matters, please don't hesitate to call directly.

Best regards,
Emanuel Ibarra
Financial Educator & Estate Planning Specialist
CEO, Hispano Financial Group LLC

---
This is an automated confirmation. Emanuel will respond personally to your inquiry soon.`
      }

      // Emanuel notification email
      const notificationEmailData = {
        to: '<EMAIL>',
        subject: `New Contact Form Submission from ${name}`,
        body: `New contact form submission received:

**Contact Details:**
Name: ${name}
Email: ${email}
Phone: ${phone || 'Not provided'}
Preferred Contact: ${preferredContact || 'Email'}

**Subject:** ${subject}

**Message:**
${message}

**Submitted:** ${new Date().toLocaleString()}

Please respond within 24 hours as promised.`
      }

      // In a real application, send emails here
      console.log('Client confirmation email would be sent:', clientEmailData)
      console.log('Emanuel notification email would be sent:', notificationEmailData)

    } catch (emailError) {
      console.error('Email sending error (non-critical):', emailError)
      // Don't fail the contact form if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Thank you for your message! Emanuel will respond within 24 hours.',
      contactId: contact.id
    })

  } catch (error) {
    console.error('Contact API error:', error)

    return NextResponse.json(
      {
        error: 'Failed to submit message. Please try again or call (************* directly.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
