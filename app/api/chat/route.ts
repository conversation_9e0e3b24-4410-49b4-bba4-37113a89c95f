
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { message, currentStep, userData, conversationHistory } = await request.json()

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      )
    }

    // Prepare conversation context
    const context = conversationHistory?.map((msg: any) => ({
      role: msg.isUser ? 'user' : 'assistant',
      content: msg.text
    })) || []

    // Enhanced system prompt with no emoji policy
    const systemPrompt = `You are <PERSON>'s intelligent AI assistant. You help potential clients understand <PERSON>'s services and guide them toward scheduling consultations.

CRITICAL: NEVER use emojis in your responses. Keep all communication professional and text-based only.

ABOUT EMANUEL:
- Financial Educator & Estate Planning Specialist
- CEO of Hispano Financial Group LLC  
- Over 10 years of financial experience
- Located in Fort Worth, TX
- Bilingual: English and Spanish
- Phone: (*************
- Email: <EMAIL>

SERVICES OFFERED:
1. Revocable Living Trusts - Avoid probate, maintain control
2. Special Needs Trusts - Protect disabled family members
3. Business Structures - LLCs, Corporations, 501(c)(3)
4. Estate Planning - Wills, Powers of Attorney
5. Financial Education - Wealth building strategies
6. Asset Protection - Shield wealth from creditors

CONVERSATION STRATEGY:
- Current step: ${currentStep || 'greeting'}
- User data collected: ${JSON.stringify(userData || {})}
- Be conversational but professional
- Ask qualifying questions to understand needs
- Guide toward consultation scheduling
- Provide educational value in each response
- Always suggest the consultation is free and personalized

RESPONSE GUIDELINES:
1. NO EMOJIS - Text only, professional tone
2. Be helpful and educational
3. Ask questions to qualify prospects
4. Encourage consultation scheduling
5. Provide specific benefits of Emanuel's services
6. Use conversational, not formal language
7. Keep responses 2-3 sentences maximum
8. End with a question or clear next step

For general questions, provide educational information but always circle back to how Emanuel can help personally.`

    // Make request to Mistral API
    const mistralResponse = await fetch('https://api.mistral.ai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.MISTRAL_API_KEY}`
      },
      body: JSON.stringify({
        model: 'mistral-large-latest',
        messages: [
          { role: 'system', content: systemPrompt },
          ...context,
          { role: 'user', content: message }
        ],
        max_tokens: 500,
        temperature: 0.7
      })
    })

    if (!mistralResponse.ok) {
      console.error('Mistral API error:', mistralResponse.status, await mistralResponse.text())
      throw new Error('Failed to get response from Mistral API')
    }

    const mistralData = await mistralResponse.json()
    const assistantMessage = mistralData.choices[0]?.message?.content

    if (!assistantMessage) {
      throw new Error('No response from Mistral API')
    }

    return NextResponse.json({ message: assistantMessage })

  } catch (error) {
    console.error('Chat API error:', error)

    // Professional fallback response without emojis
    const fallbackMessage = `I'm here to help you learn about Emanuel's financial planning services. While I work on getting you a detailed response, here's how you can connect with Emanuel directly:

Call Emanuel: (*************
Email: <EMAIL>  
Location: Fort Worth, TX

Emanuel specializes in:
- Estate Planning & Trusts
- Business Formation & Protection
- Financial Education & Wealth Building
- Asset Protection Strategies

Emanuel offers free consultations and speaks both English and Spanish. He's available Monday through Friday 9AM-6PM for immediate assistance.`

    return NextResponse.json({ message: fallbackMessage })
  }
}
