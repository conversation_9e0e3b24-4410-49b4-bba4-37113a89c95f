
import { NextRequest, NextResponse } from 'next/server'
import { google } from 'googleapis'

export async function POST(request: NextRequest) {
  try {
    const { name, email, phone, date, time, service } = await request.json()

    // For development, we'll simulate Google Calendar integration
    // In production, you would use actual Google service account credentials

    console.log('Creating Google Calendar event:', {
      name,
      email,
      phone,
      date,
      time,
      service,
      timestamp: new Date().toISOString()
    })

    // Simulate calendar event creation
    const eventData = {
      summary: `Financial Consultation - ${name}`,
      description: `
        Client: ${name}
        Email: ${email}
        Phone: ${phone}
        Service: ${service}
        
        This is a financial planning consultation with <PERSON>.
      `,
      start: {
        dateTime: `${date}T${time}:00-05:00`, // CST timezone
        timeZone: 'America/Chicago'
      },
      end: {
        dateTime: `${date}T${String(parseInt(time.split(':')[0]) + 1).padStart(2, '0')}:${time.split(':')[1]}:00-05:00`,
        timeZone: 'America/Chicago'
      },
      attendees: [
        { email: '<EMAIL>' },
        { email: email }
      ]
    }

    // In production, you would create the actual calendar event here
    // const calendar = google.calendar({ version: 'v3', auth: authClient })
    // const event = await calendar.events.insert({
    //   calendarId: 'primary',
    //   resource: eventData
    // })

    return NextResponse.json({
      success: true,
      message: 'Calendar event created successfully',
      eventId: 'simulated-' + Date.now(),
      eventData
    })

  } catch (error) {
    console.error('Google Calendar API error:', error)

    return NextResponse.json(
      { error: 'Failed to create calendar event' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get available time slots
    const today = new Date()
    const availableSlots = []

    // Generate available slots for the next 14 days (excluding weekends)
    for (let i = 1; i <= 14; i++) {
      const date = new Date(today)
      date.setDate(today.getDate() + i)

      // Skip weekends
      if (date.getDay() === 0 || date.getDay() === 6) continue

      const dateString = date.toISOString().split('T')[0]

      // Morning slots (9 AM - 12 PM)
      availableSlots.push(
        { date: dateString, time: '09:00', available: true },
        { date: dateString, time: '10:00', available: true },
        { date: dateString, time: '11:00', available: true }
      )

      // Afternoon slots (1 PM - 5 PM)
      availableSlots.push(
        { date: dateString, time: '13:00', available: true },
        { date: dateString, time: '14:00', available: true },
        { date: dateString, time: '15:00', available: true },
        { date: dateString, time: '16:00', available: true }
      )
    }

    return NextResponse.json({
      success: true,
      availableSlots
    })

  } catch (error) {
    console.error('Error fetching available slots:', error)

    return NextResponse.json(
      { error: 'Failed to fetch available slots' },
      { status: 500 }
    )
  }
}
