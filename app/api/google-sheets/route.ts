
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const appointmentData = await request.json()

    // For development, we'll simulate Google Sheets integration
    // In production, you would use actual Google service account credentials

    console.log('Adding to Google Sheets:', {
      ...appointmentData,
      timestamp: new Date().toISOString()
    })

    // Simulate adding row to Google Sheets
    const sheetData = {
      timestamp: new Date().toISOString(),
      name: appointmentData.name,
      email: appointmentData.email,
      phone: appointmentData.phone,
      service: appointmentData.service || 'Financial Consultation',
      date: appointmentData.date,
      time: appointmentData.time,
      status: 'Scheduled',
      source: 'Website',
      notes: appointmentData.notes || 'Scheduled via website'
    }

    // In production, you would append to actual Google Sheets here
    // const sheets = google.sheets({ version: 'v4', auth: authClient })
    // await sheets.spreadsheets.values.append({
    //   spreadsheetId: process.env.GOOGLE_SHEETS_ID,
    //   range: 'Appointments!A:J',
    //   valueInputOption: 'USER_ENTERED',
    //   resource: {
    //     values: [Object.values(sheetData)]
    //   }
    // })

    return NextResponse.json({
      success: true,
      message: 'Appointment tracked in Google Sheets',
      rowData: sheetData
    })

  } catch (error) {
    console.error('Google Sheets API error:', error)

    return NextResponse.json(
      { error: 'Failed to track appointment' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get booked appointments from Google Sheets
    // For development, return simulated data

    const bookedSlots = [
      { date: '2024-07-04', time: '10:00' },
      { date: '2024-07-05', time: '14:00' },
      { date: '2024-07-08', time: '09:00' }
    ]

    return NextResponse.json({
      success: true,
      bookedSlots
    })

  } catch (error) {
    console.error('Error fetching booked slots:', error)

    return NextResponse.json(
      { error: 'Failed to fetch booked slots' },
      { status: 500 }
    )
  }
}
