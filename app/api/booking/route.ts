
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const bookingData = await request.json()

    const {
      name,
      email,
      phone,
      service,
      consultationType,
      preferredDate,
      preferredTime,
      message
    } = bookingData

    // Validate required fields
    if (!name || !email || !phone || !consultationType) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Here you would typically save to a database
    // For now, we'll simulate a successful booking
    const booking = {
      id: Date.now().toString(),
      name,
      email,
      phone,
      service: service || 'General Consultation',
      consultationType,
      preferredDate: preferredDate || 'To be scheduled',
      preferredTime: preferredTime || 'To be scheduled',
      message: message || '',
      status: 'pending',
      createdAt: new Date().toISOString(),
      formType: 'booking'
    }

    // In a real application, you would:
    // 1. Save to database
    // 2. Send confirmation email to client
    // 3. Send notification email to <PERSON>
    // 4. Integrate with calendar system

    console.log('New booking received:', booking)

    // Send automated email notification (in a real app)
    try {
      // Simulate email sending
      const emailData = {
        to: email,
        subject: 'Consultation Booking Confirmation - Emanuel Ibarra Financial Services',
        body: `Dear ${name},

Thank you for scheduling a consultation with Emanuel Ibarra!

**Booking Details:**
- Service: ${service || 'General Consultation'}
- Type: ${consultationType}
- Preferred Date: ${preferredDate || 'To be scheduled'}
- Preferred Time: ${preferredTime || 'To be scheduled'}
- Phone: ${phone}
- Message: ${message || 'None provided'}

Emanuel will contact you within 24 hours to confirm your appointment details.

**Contact Information:**
- Phone: (*************
- Email: <EMAIL>
- Location: Fort Worth, TX

We look forward to helping you achieve your financial goals!

Best regards,
Emanuel Ibarra
Financial Educator & Estate Planning Specialist
Hispano Financial Group LLC`
      }

      // In a real application, send the email here
      console.log('Confirmation email would be sent:', emailData)

    } catch (emailError) {
      console.error('Email sending error (non-critical):', emailError)
      // Don't fail the booking if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Booking submitted successfully. Emanuel will contact you within 24 hours.',
      bookingId: booking.id
    })

  } catch (error) {
    console.error('Booking API error:', error)

    return NextResponse.json(
      {
        error: 'Failed to submit booking. Please try again or call (************* directly.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
