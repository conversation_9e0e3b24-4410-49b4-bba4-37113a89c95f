
'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import Calendar from 'react-calendar'
import { Check, Clock, User, Mail, Phone, MapPin, Calendar as CalendarIcon, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { googleApi, bookingApi } from '@/lib/api-config'
import 'react-calendar/dist/Calendar.css'

interface AvailableSlot {
  date: string
  time: string
  available: boolean
}

interface BookingFormData {
  name: string
  email: string
  phone: string
  service: string
  notes: string
}

export default function ReactCalendarBooking() {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [selectedTime, setSelectedTime] = useState<string>('')
  const [availableSlots, setAvailableSlots] = useState<AvailableSlot[]>([])
  const [bookedSlots, setBookedSlots] = useState<{ date: string, time: string }[]>([])
  const [currentStep, setCurrentStep] = useState<'calendar' | 'time' | 'form' | 'confirmation'>('calendar')
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState<BookingFormData>({
    name: '',
    email: '',
    phone: '',
    service: 'financial-consultation',
    notes: ''
  })
  const [bookingConfirmation, setBookingConfirmation] = useState<any>(null)

  // Fetch available slots on component mount
  useEffect(() => {
    fetchAvailableSlots()
    fetchBookedSlots()
  }, [])

  const fetchAvailableSlots = async () => {
    try {
      const data = await googleApi.calendar.getSlots()
      if (data.success) {
        setAvailableSlots(data.availableSlots)
      }
    } catch (error) {
      console.error('Error fetching available slots:', error)
    }
  }

  const fetchBookedSlots = async () => {
    try {
      const data = await googleApi.sheets.getBookedSlots()
      if (data.success) {
        setBookedSlots(data.bookedSlots)
      }
    } catch (error) {
      console.error('Error fetching booked slots:', error)
    }
  }

  const getAvailableTimesForDate = (date: Date) => {
    const dateString = date.toISOString().split('T')[0]
    const daySlots = availableSlots.filter(slot => slot.date === dateString)
    const bookedTimes = bookedSlots
      .filter(slot => slot.date === dateString)
      .map(slot => slot.time)

    return daySlots.filter(slot => !bookedTimes.includes(slot.time))
  }

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date)
    setSelectedTime('')
    setCurrentStep('time')
  }

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time)
    setCurrentStep('form')
  }

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const bookingData = {
        ...formData,
        date: selectedDate?.toISOString().split('T')[0],
        time: selectedTime
      }

      // Submit booking through main API
      const bookingResult = await bookingApi.submit(bookingData)

      // Create calendar event (with fallback)
      const calendarData = await googleApi.calendar.createEvent(bookingData)

      // Track in Google Sheets (with fallback)
      await googleApi.sheets.addBooking(bookingData)

      // Send PDF documents if needed (with fallback)
      await googleApi.drive.sendDocuments({
        email: formData.email,
        documentType: 'consultation_guide',
        clientName: formData.name
      })

      if (bookingResult.success || calendarData.success) {
        setBookingConfirmation(bookingResult.success ? bookingResult : calendarData)
        setCurrentStep('confirmation')

        // Refresh available slots
        fetchAvailableSlots()
        fetchBookedSlots()
      } else {
        throw new Error('Failed to create booking')
      }

    } catch (error) {
      console.error('Booking error:', error)
      alert('Failed to create booking. Please try again or call Emanuel directly at (817) 210-5883.')
    } finally {
      setIsLoading(false)
    }
  }

  const formatTime = (time: string) => {
    const [hour, minute] = time.split(':')
    const hourNum = parseInt(hour)
    const ampm = hourNum >= 12 ? 'PM' : 'AM'
    const displayHour = hourNum > 12 ? hourNum - 12 : hourNum === 0 ? 12 : hourNum
    return `${displayHour}:${minute} ${ampm}`
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const isDateAvailable = (date: Date) => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // Don't allow past dates
    if (date < today) return false

    // Don't allow weekends
    if (date.getDay() === 0 || date.getDay() === 6) return false

    // Check if date has available slots
    const dateString = date.toISOString().split('T')[0]
    const daySlots = availableSlots.filter(slot => slot.date === dateString)
    const bookedTimes = bookedSlots
      .filter(slot => slot.date === dateString)
      .map(slot => slot.time)

    return daySlots.some(slot => !bookedTimes.includes(slot.time))
  }

  const resetBooking = () => {
    setSelectedDate(null)
    setSelectedTime('')
    setCurrentStep('calendar')
    setFormData({
      name: '',
      email: '',
      phone: '',
      service: 'financial-consultation',
      notes: ''
    })
    setBookingConfirmation(null)
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-white mb-4">Schedule Your Free Consultation</h2>
        <p className="text-gray-400">Choose a convenient time to meet with Emanuel Ibarra</p>
      </div>

      {/* Progress Steps */}
      <div className="flex justify-center mb-8">
        <div className="flex items-center space-x-4">
          {[
            { step: 'calendar', label: 'Date', icon: CalendarIcon },
            { step: 'time', label: 'Time', icon: Clock },
            { step: 'form', label: 'Details', icon: User },
            { step: 'confirmation', label: 'Confirm', icon: Check }
          ].map((item, index) => (
            <div key={item.step} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors ${currentStep === item.step ||
                (currentStep === 'time' && item.step === 'calendar') ||
                (currentStep === 'form' && ['calendar', 'time'].includes(item.step)) ||
                (currentStep === 'confirmation' && ['calendar', 'time', 'form'].includes(item.step))
                ? 'border-blue-500 bg-blue-500 text-white'
                : 'border-gray-600 text-gray-400'
                }`}>
                <item.icon className="h-5 w-5" />
              </div>
              <span className="ml-2 text-sm text-gray-400">{item.label}</span>
              {index < 3 && <div className="w-8 h-px bg-gray-600 mx-4" />}
            </div>
          ))}
        </div>
      </div>

      {/* Calendar Selection */}
      {currentStep === 'calendar' && (
        <Card className="bg-gray-900 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">Select a Date</CardTitle>
            <CardDescription className="text-gray-400">
              Choose from available weekdays. Weekends are not available.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-center">
              <Calendar
                onChange={(value) => handleDateSelect(value as Date)}
                value={selectedDate}
                tileDisabled={({ date }) => !isDateAvailable(date)}
                className="react-calendar-dark"
                minDate={new Date()}
                maxDate={new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)} // 30 days ahead
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Time Selection */}
      {currentStep === 'time' && selectedDate && (
        <Card className="bg-gray-900 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">Select a Time</CardTitle>
            <CardDescription className="text-gray-400">
              Available slots for {formatDate(selectedDate)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {getAvailableTimesForDate(selectedDate).map((slot) => (
                <Button
                  key={slot.time}
                  onClick={() => handleTimeSelect(slot.time)}
                  variant="outline"
                  className="border-gray-600 text-white hover:bg-blue-600 hover:border-blue-600"
                >
                  {formatTime(slot.time)}
                </Button>
              ))}
            </div>
            <div className="mt-6">
              <Button
                onClick={() => setCurrentStep('calendar')}
                variant="ghost"
                className="text-gray-400 hover:text-white"
              >
                ← Back to Calendar
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Form */}
      {currentStep === 'form' && (
        <Card className="bg-gray-900 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">Your Information</CardTitle>
            <CardDescription className="text-gray-400">
              Appointment: {selectedDate && formatDate(selectedDate)} at {formatTime(selectedTime)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleFormSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name" className="text-white">Full Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="bg-gray-800 border-gray-600 text-white"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="email" className="text-white">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    className="bg-gray-800 border-gray-600 text-white"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone" className="text-white">Phone Number *</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    className="bg-gray-800 border-gray-600 text-white"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="service" className="text-white">Service Type</Label>
                  <Select value={formData.service} onValueChange={(value) => setFormData(prev => ({ ...prev, service: value }))}>
                    <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-600">
                      <SelectItem value="financial-consultation">Financial Consultation</SelectItem>
                      <SelectItem value="estate-planning">Estate Planning</SelectItem>
                      <SelectItem value="trust-setup">Trust Setup</SelectItem>
                      <SelectItem value="business-formation">Business Formation</SelectItem>
                      <SelectItem value="asset-protection">Asset Protection</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="notes" className="text-white">Additional Notes (Optional)</Label>
                <textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  className="w-full p-3 bg-gray-800 border border-gray-600 rounded-md text-white resize-none"
                  rows={3}
                  placeholder="Tell us about your specific needs or questions..."
                />
              </div>

              <div className="flex justify-between">
                <Button
                  type="button"
                  onClick={() => setCurrentStep('time')}
                  variant="ghost"
                  className="text-gray-400 hover:text-white"
                >
                  ← Back to Time
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Scheduling...
                    </>
                  ) : (
                    'Schedule Appointment'
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Confirmation */}
      {currentStep === 'confirmation' && bookingConfirmation && (
        <Card className="bg-gray-900 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Check className="h-6 w-6 text-green-500 mr-2" />
              Appointment Confirmed!
            </CardTitle>
            <CardDescription className="text-gray-400">
              Your consultation with Emanuel Ibarra has been scheduled successfully.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-gray-800 p-4 rounded-lg">
              <h3 className="text-white font-semibold mb-3">Appointment Details</h3>
              <div className="space-y-2 text-gray-300">
                <div className="flex items-center">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  {selectedDate && formatDate(selectedDate)}
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2" />
                  {formatTime(selectedTime)}
                </div>
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-2" />
                  {formData.name}
                </div>
                <div className="flex items-center">
                  <Mail className="h-4 w-4 mr-2" />
                  {formData.email}
                </div>
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2" />
                  {formData.phone}
                </div>
              </div>
            </div>

            <div className="bg-blue-900/30 border border-blue-500 p-4 rounded-lg">
              <h4 className="text-blue-400 font-semibold mb-2">What's Next?</h4>
              <ul className="text-gray-300 space-y-1 text-sm">
                <li>• You'll receive a confirmation email with calendar invite</li>
                <li>• Emanuel will call you 24 hours before the appointment</li>
                <li>• Consultation documents will be sent to your email</li>
                <li>• Meeting can be in-person or virtual (will be confirmed via call)</li>
              </ul>
            </div>

            <div className="text-center">
              <Button
                onClick={resetBooking}
                variant="outline"
                className="border-gray-600 text-white hover:bg-gray-800"
              >
                Schedule Another Appointment
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
