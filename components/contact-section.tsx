
'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Phone, Mail, MapPin, Clock, MessageCircle, Send } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { useLanguage } from '@/components/navigation'
import { contactApi } from '@/lib/api-config'

export default function ContactSection() {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  })

  const { language } = useLanguage()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const result = await contactApi.submit({
        ...formData,
        language,
        form_type: 'contact'
      })

      if (result.success) {
        toast({
          title: language === 'en' ? 'Message Sent!' : '¡Mensaje Enviado!',
          description: language === 'en'
            ? 'Thank you for your message. Emanuel will get back to you within 24 hours.'
            : 'Gracias por tu mensaje. Emanuel te responderá dentro de 24 horas.',
        })
        setFormData({ name: '', email: '', phone: '', subject: '', message: '' })
      } else {
        throw new Error('Failed to send message')
      }
    } catch (error) {
      toast({
        title: language === 'en' ? 'Error' : 'Error',
        description: language === 'en'
          ? 'Failed to send message. Please try calling directly.'
          : 'Error al enviar mensaje. Por favor intenta llamar directamente.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleWhatsApp = () => {
    const message = encodeURIComponent(
      language === 'es'
        ? 'Hola Emanuel, me gustaría programar una consulta sobre planificación financiera.'
        : 'Hello Emanuel, I would like to schedule a consultation about financial planning.'
    )
    window.open(`https://wa.me/18172105883?text=${message}`, '_blank')
  }

  const contactInfo = [
    {
      icon: Phone,
      title: { en: 'Phone', es: 'Teléfono' },
      content: '(*************',
      action: () => window.open('tel:+18172105883'),
      clickable: true
    },
    {
      icon: Mail,
      title: { en: 'Email', es: 'Correo' },
      content: '<EMAIL>',
      action: () => window.open('mailto:<EMAIL>'),
      clickable: true
    },
    {
      icon: MapPin,
      title: { en: 'Location', es: 'Ubicación' },
      content: 'Fort Worth, TX',
      action: null,
      clickable: false
    },
    {
      icon: Clock,
      title: { en: 'Hours', es: 'Horarios' },
      content: language === 'en' ? 'Mon-Fri: 9AM-6PM' : 'Lun-Vie: 9AM-6PM',
      action: null,
      clickable: false
    }
  ]

  return (
    <section id="contact" className="section-black py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          className="text-center mb-16 scroll-reveal"
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {language === 'en' ? 'Get In Touch' : 'Ponte en Contacto'}
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            {language === 'en'
              ? 'Ready to secure your financial future? Let\'s discuss your goals and create a personalized plan.'
              : '¿Listo para asegurar tu futuro financiero? Hablemos de tus objetivos y creemos un plan personalizado.'
            }
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            className="scroll-slide-left"
            initial={{ opacity: 0, x: -50 }}
            animate={inView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8 }}
          >
            <div className="enhanced-card p-8 rounded-xl">
              <h3 className="text-2xl font-semibold text-white mb-6">
                {language === 'en' ? 'Send a Message' : 'Enviar Mensaje'}
              </h3>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid sm:grid-cols-2 gap-4">
                  <div>
                    <Input
                      name="name"
                      placeholder={language === 'en' ? 'Your Name' : 'Tu Nombre'}
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="bg-gray-800 border-accent-blue/30 text-white placeholder-gray-400"
                    />
                  </div>
                  <div>
                    <Input
                      name="email"
                      type="email"
                      placeholder={language === 'en' ? 'Your Email' : 'Tu Correo'}
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="bg-gray-800 border-accent-blue/30 text-white placeholder-gray-400"
                    />
                  </div>
                </div>
                <div className="grid sm:grid-cols-2 gap-4">
                  <div>
                    <Input
                      name="phone"
                      type="tel"
                      placeholder={language === 'en' ? 'Your Phone' : 'Tu Teléfono'}
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="bg-gray-800 border-accent-blue/30 text-white placeholder-gray-400"
                    />
                  </div>
                  <div>
                    <Input
                      name="subject"
                      placeholder={language === 'en' ? 'Subject' : 'Asunto'}
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className="bg-gray-800 border-accent-blue/30 text-white placeholder-gray-400"
                    />
                  </div>
                </div>
                <div>
                  <Textarea
                    name="message"
                    placeholder={language === 'en' ? 'Your Message' : 'Tu Mensaje'}
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={4}
                    className="bg-gray-800 border-accent-blue/30 text-white placeholder-gray-400"
                  />
                </div>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="btn-primary flex items-center gap-2 flex-1"
                  >
                    <Send className="h-4 w-4" />
                    {isSubmitting
                      ? (language === 'en' ? 'Sending...' : 'Enviando...')
                      : (language === 'en' ? 'Send Message' : 'Enviar Mensaje')
                    }
                  </Button>
                  <Button
                    type="button"
                    onClick={handleWhatsApp}
                    className="whatsapp-btn flex items-center gap-2 flex-1"
                  >
                    <MessageCircle className="h-4 w-4" />
                    {language === 'en' ? 'WhatsApp' : 'WhatsApp'}
                  </Button>
                </div>
              </form>
            </div>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            className="scroll-slide-right"
            initial={{ opacity: 0, x: 50 }}
            animate={inView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="space-y-6">
              <h3 className="text-2xl font-semibold text-white mb-6">
                {language === 'en' ? 'Contact Information' : 'Información de Contacto'}
              </h3>

              <div className="space-y-4">
                {contactInfo.map((info, index) => (
                  <motion.div
                    key={index}
                    className={`enhanced-card p-6 rounded-xl ${info.clickable ? 'cursor-pointer hover-glow' : ''}`}
                    onClick={info.action || undefined}
                    initial={{ opacity: 0, y: 20 }}
                    animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                    transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-accent-blue rounded-lg flex items-center justify-center">
                        <info.icon className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h4 className="text-white font-medium">{info.title[language]}</h4>
                        <p className={`text-gray-400 ${info.clickable ? 'hover:text-blue-400 transition-colors' : ''}`}>
                          {info.content}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              <motion.div
                className="enhanced-card p-6 rounded-xl mt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: 0.8 }}
              >
                <h4 className="text-white font-medium mb-3">
                  {language === 'en' ? 'Emergency Contact' : 'Contacto de Emergencia'}
                </h4>
                <p className="text-gray-400 text-sm leading-relaxed">
                  {language === 'en'
                    ? 'For urgent financial matters outside business hours, you can reach Emanuel via WhatsApp or leave a detailed voicemail.'
                    : 'Para asuntos financieros urgentes fuera del horario comercial, puedes contactar a Emanuel vía WhatsApp o dejar un mensaje de voz detallado.'
                  }
                </p>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
