
'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { MessageCircle, X, Send, Phone, Calendar, Loader2, User, Bot } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { chatApi, consultationApi } from '@/lib/api-config'

interface Message {
  id: string
  text: string
  isUser: boolean
  timestamp: Date
  isTyping?: boolean
}

interface UserData {
  name: string
  email: string
  phone: string
  language: 'en' | 'es'
  consultationType: string
  appointmentTime: string
}

type ChatStep = 'greeting' | 'qualifying' | 'collecting_info' | 'scheduling' | 'confirming'

export default function Chatbot() {
  const [isOpen, setIsOpen] = useState(false)
  const [currentStep, setCurrentStep] = useState<ChatStep>('greeting')
  const [userData, setUserData] = useState<UserData>({
    name: '',
    email: '',
    phone: '',
    language: 'en',
    consultationType: '',
    appointmentTime: ''
  })
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Hello! I\'m Emanuel\'s AI assistant. I help connect people with Emanuel for personalized financial planning and estate planning consultations. What brings you here today?',
      isUser: false,
      timestamp: new Date()
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [awaitingInfo, setAwaitingInfo] = useState<'name' | 'email' | 'phone' | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen])

  // Smart conversation flow based on uploaded logic
  const handleSendMessage = async () => {
    if (!inputValue.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputValue.trim(),
      isUser: true,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    const userInput = inputValue.trim().toLowerCase()
    setInputValue('')
    setIsLoading(true)

    // Add typing indicator
    const typingMessage: Message = {
      id: 'typing',
      text: '',
      isUser: false,
      timestamp: new Date(),
      isTyping: true
    }
    setMessages(prev => [...prev, typingMessage])

    // Smart response logic based on current step
    let botResponse = ''

    try {
      if (awaitingInfo) {
        // Handle collecting user information
        if (awaitingInfo === 'name') {
          setUserData(prev => ({ ...prev, name: inputValue.trim() }))
          setAwaitingInfo('email')
          botResponse = `Thank you, ${inputValue.trim()}! Now I need your email address to send you the consultation details.`
        } else if (awaitingInfo === 'email') {
          if (userInput.includes('@')) {
            setUserData(prev => ({ ...prev, email: inputValue.trim() }))
            setAwaitingInfo('phone')
            botResponse = 'Perfect! Last step - could you provide your phone number? Emanuel prefers to confirm appointments personally.'
          } else {
            botResponse = 'Please provide a valid email address so Emanuel can send you the consultation details.'
          }
        } else if (awaitingInfo === 'phone') {
          if (userInput.match(/[\d\-\(\)\s+]{10,}/)) {
            setUserData(prev => ({ ...prev, phone: inputValue.trim() }))
            setAwaitingInfo(null)
            setCurrentStep('scheduling')
            botResponse = 'Excellent! Now let me show you Emanuel\'s available times. He has openings this week for 30-minute consultations. Would you prefer morning (9AM-12PM) or afternoon (1PM-5PM) slots?'
          } else {
            botResponse = 'Please provide a valid phone number so Emanuel can reach you to confirm the appointment.'
          }
        }
      } else if (currentStep === 'greeting') {
        // Initial qualification
        if (userInput.includes('trust') || userInput.includes('estate') || userInput.includes('will') || userInput.includes('business') || userInput.includes('llc') || userInput.includes('financial')) {
          setCurrentStep('qualifying')
          botResponse = 'Great! Emanuel specializes in exactly those areas. He helps families and entrepreneurs protect their wealth through trusts, estate planning, and business structures. Are you looking to protect family assets, start a business, or plan for the future?'
        } else if (userInput.includes('hello') || userInput.includes('hi') || userInput.includes('hola')) {
          botResponse = 'Welcome! Emanuel helps people with financial planning, estate planning, trusts, and business formation. What specific area interests you most?'
        } else {
          // Use AI for general responses
          await handleAIResponse(userMessage.text)
          return
        }
      } else if (currentStep === 'qualifying') {
        if (userInput.includes('family') || userInput.includes('asset') || userInput.includes('protect') || userInput.includes('business') || userInput.includes('plan')) {
          setCurrentStep('collecting_info')
          setAwaitingInfo('name')
          botResponse = 'Perfect! Emanuel can definitely help you with that. Let me schedule a free 30-minute consultation where he can provide personalized advice for your situation. What\'s your name?'
        } else {
          await handleAIResponse(userMessage.text)
          return
        }
      } else if (currentStep === 'scheduling') {
        if (userInput.includes('morning') || userInput.includes('9') || userInput.includes('10') || userInput.includes('11') || userInput.includes('12')) {
          setUserData(prev => ({ ...prev, appointmentTime: 'morning' }))
          setCurrentStep('confirming')
          botResponse = 'Perfect! I\'ll have Emanuel call you tomorrow morning to schedule your consultation. You can expect his call between 9AM-12PM. He\'ll discuss your specific needs and find the best time that works for both of you.'
        } else if (userInput.includes('afternoon') || userInput.includes('1') || userInput.includes('2') || userInput.includes('3') || userInput.includes('4') || userInput.includes('5')) {
          setUserData(prev => ({ ...prev, appointmentTime: 'afternoon' }))
          setCurrentStep('confirming')
          botResponse = 'Excellent! I\'ll have Emanuel call you tomorrow afternoon to schedule your consultation. You can expect his call between 1PM-5PM. He\'ll discuss your specific needs and find the best time that works for both of you.'
        } else {
          botResponse = 'Would you prefer a morning call (9AM-12PM) or afternoon call (1PM-5PM) from Emanuel to schedule your consultation?'
        }
      } else if (currentStep === 'confirming') {
        botResponse = 'Your consultation request has been submitted! Emanuel will call you personally to schedule your meeting. In the meantime, feel free to ask me any general questions about his services.'
        // Here you would normally save to database
        await saveConsultationRequest()
      }

      // Add bot response
      setTimeout(() => {
        setMessages(prev => {
          const withoutTyping = prev.filter(m => m.id !== 'typing')
          return [...withoutTyping, {
            id: Date.now().toString(),
            text: botResponse,
            isUser: false,
            timestamp: new Date()
          }]
        })
        setIsLoading(false)
      }, 1000)

    } catch (error) {
      console.error('Chatbot error:', error)
      setMessages(prev => {
        const withoutTyping = prev.filter(m => m.id !== 'typing')
        return [...withoutTyping, {
          id: Date.now().toString(),
          text: 'I apologize for the technical issue. Please call Emanuel directly at (817) 210-5883 <NAME_EMAIL> to schedule your consultation.',
          isUser: false,
          timestamp: new Date()
        }]
      })
      setIsLoading(false)
    }
  }

  // AI response for general questions
  const handleAIResponse = async (message: string) => {
    try {
      const data = await chatApi.sendMessage({
        message,
        currentStep,
        userData,
        conversationHistory: messages.filter(m => !m.isTyping).slice(-10)
      })

      setMessages(prev => {
        const withoutTyping = prev.filter(m => m.id !== 'typing')
        return [...withoutTyping, {
          id: Date.now().toString(),
          text: data.message,
          isUser: false,
          timestamp: new Date()
        }]
      })
    } catch (error) {
      console.error('AI response error:', error)
      setMessages(prev => {
        const withoutTyping = prev.filter(m => m.id !== 'typing')
        return [...withoutTyping, {
          id: Date.now().toString(),
          text: 'I\'m here to help you connect with Emanuel for a consultation. You can reach him directly at (817) 210-5883 <NAME_EMAIL>.',
          isUser: false,
          timestamp: new Date()
        }]
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Save consultation request
  const saveConsultationRequest = async () => {
    try {
      await consultationApi.submit(userData)
    } catch (error) {
      console.error('Failed to save consultation request:', error)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const quickActions = [
    {
      text: 'Schedule Consultation',
      icon: Calendar,
      action: () => {
        document.getElementById('booking')?.scrollIntoView({ behavior: 'smooth' })
        setIsOpen(false)
      }
    },
    {
      text: 'Call Emanuel',
      icon: Phone,
      action: () => window.open('tel:+***********')
    }
  ]

  const suggestedQuestions = [
    'I need help with estate planning',
    'I want to protect my business assets',
    'Tell me about trusts and how they work',
    'I need financial planning advice',
    'What business structure should I choose?',
    'How can I schedule a consultation?'
  ]

  return (
    <div className="chatbot-container">
      {/* Chat Toggle Button with blue accent */}
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="chatbot-toggle"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 2 }}
      >
        {isOpen ? <X className="h-6 w-6" /> : <MessageCircle className="h-6 w-6" />}
      </motion.button>

      {/* Chat Window with enhanced styling */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="chatbot-window"
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ duration: 0.3 }}
          >
            {/* Header with blue accent */}
            <div className="bg-gradient-to-r from-black to-gray-900 text-white p-4 border-b border-accent-blue/30">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-accent-blue rounded-full flex items-center justify-center">
                    <Bot className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-sm">Emanuel's AI Assistant</h3>
                    <p className="text-xs text-gray-300">Financial Planning Expert</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-xs text-gray-300">Online</span>
                </div>
              </div>
            </div>

            {/* Messages */}
            <ScrollArea className="flex-1 p-4 h-80 bg-black">
              <div className="space-y-4">
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="flex items-start space-x-2 max-w-[85%]">
                      {!message.isUser && (
                        <div className="w-6 h-6 bg-accent-blue rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                          <Bot className="h-3 w-3 text-white" />
                        </div>
                      )}
                      <div
                        className={`p-3 rounded-lg text-sm ${message.isUser
                          ? 'bg-accent-blue text-white rounded-br-none'
                          : 'bg-gray-800 text-gray-100 rounded-bl-none'
                          }`}
                      >
                        {message.isTyping ? (
                          <div className="flex items-center space-x-1">
                            <div className="flex space-x-1">
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                            </div>
                            <span className="text-xs text-gray-500 ml-2">Typing...</span>
                          </div>
                        ) : (
                          <>
                            <p className="leading-relaxed">{message.text}</p>
                            <p className={`text-xs mt-1 ${message.isUser ? 'text-blue-200' : 'text-gray-500'}`}>
                              {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </p>
                          </>
                        )}
                      </div>
                      {message.isUser && (
                        <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                          <User className="h-3 w-3 text-white" />
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
                <div ref={messagesEndRef} />
              </div>

              {/* Suggested Questions */}
              {messages.length === 1 && (
                <motion.div
                  className="mt-6 space-y-2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  <p className="text-xs text-gray-400 font-medium">Suggested questions:</p>
                  <div className="space-y-2">
                    {suggestedQuestions.slice(0, 3).map((question, index) => (
                      <motion.button
                        key={question}
                        onClick={() => {
                          setInputValue(question)
                          setTimeout(handleSendMessage, 100)
                        }}
                        className="block w-full text-left p-2 text-xs bg-gray-800 hover:bg-gray-700 rounded-md transition-colors text-gray-200"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.7 + index * 0.1 }}
                      >
                        {question}
                      </motion.button>
                    ))}
                  </div>
                </motion.div>
              )}
            </ScrollArea>

            {/* Quick Actions */}
            <div className="border-t border-accent-blue/30 p-3 bg-black">
              <div className="flex gap-2 mb-3">
                {quickActions.map((action) => (
                  <Button
                    key={action.text}
                    onClick={action.action}
                    variant="outline"
                    size="sm"
                    className="flex-1 text-xs border-accent-blue/30 text-white hover:bg-accent-blue hover:border-accent-blue"
                  >
                    <action.icon className="h-3 w-3 mr-1" />
                    {action.text}
                  </Button>
                ))}
              </div>

              {/* Input */}
              <div className="flex space-x-2">
                <Input
                  ref={inputRef}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask me about financial planning..."
                  className="flex-1 text-sm bg-gray-800 border-accent-blue/30 text-white placeholder-gray-400"
                  disabled={isLoading}
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isLoading}
                  size="sm"
                  className="bg-accent-blue hover:bg-blue-700"
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>

              <p className="text-xs text-gray-500 mt-2 text-center">
                Powered by AI • For detailed advice, schedule a consultation
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
