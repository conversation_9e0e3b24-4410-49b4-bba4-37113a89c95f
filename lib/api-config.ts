// API Configuration for <PERSON>'s website
// This file centralizes all API endpoint configurations

const isDevelopment = process.env.NODE_ENV === 'development'
const isStatic = typeof window !== 'undefined' && !isDevelopment

// Netlify Functions base URL
const NETLIFY_FUNCTIONS_BASE = 'https://emanuel-api-functions.netlify.app/.netlify/functions'

// Local development API base URL
const LOCAL_API_BASE = '/api'

// Get the appropriate base URL based on environment
const getApiBase = () => {
  if (isStatic) {
    return NETLIFY_FUNCTIONS_BASE
  }
  return LOCAL_API_BASE
}

// API endpoints configuration
export const API_ENDPOINTS = {
  // Core functionality endpoints
  booking: `${getApiBase()}/booking`,
  contact: `${getApiBase()}/contact`,
  chat: `${getApiBase()}/chat`,
  consultation: `${getApiBase()}/consultation`,
  
  // Google integration endpoints (these will fallback gracefully)
  googleCalendar: `${getApiBase()}/google-calendar`,
  googleSheets: `${getApiBase()}/google-sheets`,
  googleDrive: `${getApiBase()}/google-drive`,
}

// Helper function to make API calls with proper error handling
export const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  }

  try {
    const response = await fetch(endpoint, defaultOptions)
    
    if (!response.ok) {
      throw new Error(`API call failed: ${response.status} ${response.statusText}`)
    }
    
    return await response.json()
  } catch (error) {
    console.error('API call error:', error)
    throw error
  }
}

// Specific API call functions
export const bookingApi = {
  submit: (data: any) => apiCall(API_ENDPOINTS.booking, {
    method: 'POST',
    body: JSON.stringify(data),
  }),
}

export const contactApi = {
  submit: (data: any) => apiCall(API_ENDPOINTS.contact, {
    method: 'POST',
    body: JSON.stringify(data),
  }),
}

export const chatApi = {
  sendMessage: (data: any) => apiCall(API_ENDPOINTS.chat, {
    method: 'POST',
    body: JSON.stringify(data),
  }),
}

export const consultationApi = {
  submit: (data: any) => apiCall(API_ENDPOINTS.consultation, {
    method: 'POST',
    body: JSON.stringify(data),
  }),
}

// Google integration APIs (with fallback handling)
export const googleApi = {
  calendar: {
    getSlots: async () => {
      try {
        return await apiCall(API_ENDPOINTS.googleCalendar)
      } catch (error) {
        console.warn('Google Calendar API not available, using fallback')
        return { success: true, availableSlots: [] }
      }
    },
    createEvent: async (data: any) => {
      try {
        return await apiCall(API_ENDPOINTS.googleCalendar, {
          method: 'POST',
          body: JSON.stringify(data),
        })
      } catch (error) {
        console.warn('Google Calendar API not available, using fallback')
        return { success: true, message: 'Booking request received. Emanuel will contact you to confirm.' }
      }
    },
  },
  sheets: {
    getBookedSlots: async () => {
      try {
        return await apiCall(API_ENDPOINTS.googleSheets)
      } catch (error) {
        console.warn('Google Sheets API not available, using fallback')
        return { success: true, bookedSlots: [] }
      }
    },
    addBooking: async (data: any) => {
      try {
        return await apiCall(API_ENDPOINTS.googleSheets, {
          method: 'POST',
          body: JSON.stringify(data),
        })
      } catch (error) {
        console.warn('Google Sheets API not available, using fallback')
        return { success: true }
      }
    },
  },
  drive: {
    sendDocuments: async (data: any) => {
      try {
        return await apiCall(API_ENDPOINTS.googleDrive, {
          method: 'POST',
          body: JSON.stringify(data),
        })
      } catch (error) {
        console.warn('Google Drive API not available, using fallback')
        return { success: true }
      }
    },
  },
}
